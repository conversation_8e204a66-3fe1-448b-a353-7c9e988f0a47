# ContentMaster Pro - النسخة المحدثة 🚀

نظام إنتاج المحتوى المتقدم باستخدام الذكاء الاصطناعي مع دعم مفاتيح API متعددة ونماذج متنوعة

## ✨ الميزات الجديدة

### 🔑 إدارة مفاتيح API متقدمة
- دعم مفاتيح Gemini API متعددة مع تدوير تلقائي
- دعم مفاتيح OpenRouter API للوصول لنماذج متنوعة
- إحصائيات استخدام مفصلة وإدارة ذكية للحدود

### 🤖 نماذج ذكية متنوعة
- **6 نماذج Gemini مجانية**: من Flash 8B السريع إلى Pro المتقدم
- **5+ نماذج OpenRouter مجانية**: Phi-3, Llama 3.1, Gemma, Qwen
- **توزيع ذكي للمهام**: اختيار النموذج الأمثل لكل مهمة تلقائياً

### 🖼️ إنتاج الصور التلقائي
- إنتاج صور مناسبة للمقالات من مصادر مجانية
- إنشاء صور نصية احترافية كبديل
- تحسين تلقائي وحفظ منظم

### 🎨 واجهة محسنة بالكامل
- تصميم عصري مع ألوان متدرجة وأيقونات جذابة
- سجل عمليات ملون مع فلترة حسب نوع الرسالة
- شريط حالة متقدم مع معلومات الوقت والنموذج الحالي
- tooltips تفاعلية وتأثيرات hover للأزرار

## التشغيل السريع

1. **تثبيت المتطلبات**: `install.bat`
2. **تشغيل البرنامج**: `run_gui.bat`

## الإعداد

### إضافة موقع WordPress:
1. انتقل إلى تبويب "إدارة المواقع"
2. أدخل بيانات الموقع
3. احصل على App Password من WordPress

### تخصيص الـ Prompts:
- عدل ملفات `prompts/*.txt` حسب احتياجاتك

## الاستخدام

1. أدخل الكلمة المفتاحية
2. اختر الموقع للنشر
3. أنتج المقالات
4. انشر المحتوى

## الميزات الإضافية

### القائمة السياقية (كليك يمين):
- نسخ ولصق في جميع حقول النص
- اختصارات لوحة المفاتيح: Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+A
- دعم كامل للنصوص العربية

### إدارة المواقع المتعددة:
- إضافة وحذف المواقع بسهولة
- التبديل بين المواقع للنشر
- حفظ دائم لبيانات المواقع (لا تفقد بعد إغلاق البرنامج)
- ملف إعدادات JSON قابل للنسخ الاحتياطي

### Prompts قابلة للتعديل:
- تخصيص توجيهات الذكاء الاصطناعي
- ملفات منفصلة سهلة التعديل
- تحديث فوري للتغييرات

### الروابط الداخلية الذكية المرتبطة بالتصنيفات:
- مزامنة تدريجية ذكية للمشاركات (جلب الجديد فقط)
- جلب سريع وموثوق بدون انقطاع اتصال
- ربط ذكي بناءً على التشابه في المحتوى والتصنيف
- أولوية عالية للمشاركات من نفس التصنيف (85% من الروابط)
- إضافة رابط كل 400 كلمة مع التوزيع المثالي
- تمييز بصري للروابط من نفس التصنيف (🎯)
- إحصائيات شاملة لتتبع المزامنة
- تحسين SEO الداخلي المتقدم للموقع

### التصنيفات التلقائية:
- جلب التصنيفات من WordPress مباشرة
- اختيار التصنيف المناسب عند إنشاء المقال
- تخزين محلي للتصنيفات لسرعة الوصول
- نشر المقالات في التصنيف الصحيح تلقائياً

## الملفات الأساسية

- `gui.py` - الواجهة الرسومية
- `config.py` - الإعدادات
- `prompts/` - قوالب الـ prompts
- `run_gui.bat` - تشغيل البرنامج
