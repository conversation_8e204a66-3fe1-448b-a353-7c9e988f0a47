"""
مدير المحتوى لإنتاج المقالات باستخدام الذكاء الاصطناعي

تم تحديث النظام لفصل التاجز عن الكلمات المفتاحية:
1. الكلمات المفتاحية (Keywords): تستخدم لإنشاء المقالات الفرعية وتخزن في ملف [keyword].txt
2. التاجز (Tags): تستخدم للتصنيف في ووردبريس وتخزن في ملف tags-[keyword].txt

الكلمات المفتاحية تستخرج من المقال الرئيسي من سطور # tags: وتستخدم لإنشاء المقالات الفرعية.
التاجز تنشأ من الكلمات المفتاحية باستخدام دالة _generate_tags_from_keywords وتخزن في ملف منفصل.
"""

import os
import time
import random
import sqlite3
import re
import json
from typing import List, Dict, Tuple
from datetime import datetime

import google.generativeai as genai

from config import Config
from logger import logger
import database as db
from posts_manager import PostsManager
from openrouter_client import openrouter_client
from image_generator import image_generator

class ContentManager:
    def __init__(self):
        """تهيئة مدير المحتوى"""
        self._setup_ai()
        Config.ensure_directories()
        # تهيئة قاعدة البيانات
        db.initialize()
        logger.info("تم تهيئة مدير المحتوى بنجاح")
    
    def _setup_ai(self) -> None:
        """إعداد الذكاء الاصطناعي مع دعم النظام الجديد"""
        try:
            current_model = Config.get_current_model()
            model_settings = Config.get_model_settings()

            # إعداد مفتاح API بناءً على نوع النموذج
            if current_model.provider == "gemini":
                api_key = Config.get_smart_api_key("gemini")
                if not api_key:
                    raise RuntimeError("لا يوجد مفتاح Gemini API متاح")

                genai.configure(api_key=api_key)
                self.model = genai.GenerativeModel(
                    model_name=current_model.model_id,
                    generation_config=genai.types.GenerationConfig(
                        temperature=model_settings.get("temperature", current_model.temperature),
                        top_p=model_settings.get("top_p", getattr(current_model, 'top_p', 0.9)),
                        top_k=model_settings.get("top_k", getattr(current_model, 'top_k', 40)),
                        max_output_tokens=current_model.max_tokens
                    )
                )
                self.current_api_key = api_key

            elif current_model.provider == "openrouter":
                api_key = Config.get_smart_api_key("openrouter")
                if not api_key:
                    raise RuntimeError("لا يوجد مفتاح OpenRouter API متاح")

                # إعداد OpenRouter
                self.current_api_key = api_key
                self.openrouter_client = openrouter_client

                # اختبار الاتصال
                if not self.openrouter_client.test_connection():
                    logger.warning("فشل في اختبار الاتصال مع OpenRouter")
                else:
                    logger.success("تم الاتصال بـ OpenRouter بنجاح")

            else:
                raise RuntimeError(f"نوع نموذج غير مدعوم: {current_model.provider}")

            self.current_model_info = current_model
            self.model_settings = model_settings

            logger.success(f"تم إعداد الذكاء الاصطناعي بنجاح - النموذج: {current_model.name}")
            logger.info(f"المزود: {current_model.provider}, درجة الحرارة: {model_settings.get('temperature')}")
            logger.info(f"مفتاح API: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")

        except Exception as e:
            logger.error("فشل في إعداد الذكاء الاصطناعي", e)
            raise
    
    def _load_prompt_template(self, prompt_file: str) -> str:
        """تحميل قالب الـ prompt من ملف"""
        try:
            prompt_path = os.path.join("prompts", prompt_file)
            if not os.path.exists(prompt_path):
                logger.error(f"ملف الـ prompt غير موجود: {prompt_path}")
                return ""

            with open(prompt_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.error(f"فشل في تحميل ملف الـ prompt: {prompt_file}", e)
            return ""

    def _get_main_article_prompt(self, keyword: str) -> str:
        """إنشاء prompt للمقال الرئيسي من الملف المخصص"""
        template = self._load_prompt_template("main_article_prompt.txt")
        if not template:
            logger.error("فشل في تحميل قالب المقال الرئيسي")
            return ""

        return template.format(keyword=keyword)
    
    def _get_sub_article_prompt(self, main_keyword: str, sub_keyword: str, keywords: List[str] = None) -> str:
        """إنشاء prompt للمقال الفرعي من الملف المخصص"""
        template = self._load_prompt_template("sub_article_prompt.txt")
        if not template:
            logger.error("فشل في تحميل قالب المقال الفرعي")
            return ""

        # تحميل التاجز من الملف إذا كانت موجودة
        tags = self._load_tags(main_keyword)
        
        # إذا لم تكن هناك تاجز، قم بإنشائها من الكلمات المفتاحية
        if not tags and keywords:
            tags = self._generate_tags_from_keywords(keywords)
        
        tags_line = ", ".join(tags) if tags else ""
        return template.format(
            main_kw=main_keyword,
            sub_kw=sub_keyword,
            sub_keyword=sub_keyword,
            tags_line=tags_line
        )
    
    def switch_model(self, model_id: str) -> bool:
        """تبديل النموذج المستخدم"""
        try:
            if Config.set_current_model(model_id):
                self._setup_ai()  # إعادة إعداد النموذج
                logger.success(f"تم تبديل النموذج إلى: {self.current_model_info.name}")
                return True
            else:
                logger.error(f"فشل في تبديل النموذج إلى: {model_id}")
                return False
        except Exception as e:
            logger.error(f"خطأ في تبديل النموذج: {model_id}", e)
            return False

    def generate_article_image(self, keyword: str, article_title: str = None) -> Optional[str]:
        """إنتاج صورة للمقال"""
        try:
            if not article_title:
                article_title = keyword

            logger.info(f"بدء إنتاج صورة للمقال: {keyword}")
            image_path = image_generator.generate_article_image(keyword, article_title)

            if image_path:
                logger.success(f"تم إنتاج صورة للمقال: {image_path}")
                return image_path
            else:
                logger.warning(f"فشل في إنتاج صورة للمقال: {keyword}")
                return None

        except Exception as e:
            logger.error(f"خطأ في إنتاج صورة للمقال: {keyword}", e)
            return None

    def get_current_model_info(self) -> dict:
        """الحصول على معلومات النموذج الحالي"""
        model_settings = self.model_settings if hasattr(self, 'model_settings') else Config.get_model_settings()
        
        return {
            "id": self.current_model_info.model_id,
            "name": self.current_model_info.name,
            "description": self.current_model_info.description,
            "max_tokens": self.current_model_info.max_tokens,
            "temperature": model_settings.get("temperature", self.current_model_info.temperature),
            "top_p": model_settings.get("top_p", self.current_model_info.top_p),
            "top_k": model_settings.get("top_k", self.current_model_info.top_k),
            "max_retries": model_settings.get("max_retries", 3),
            "retry_delay": model_settings.get("retry_delay", 5),
            "rate_limit_delay": model_settings.get("rate_limit_delay", (6, 15))
        }
    
    def get_available_models(self) -> dict:
        """الحصول على قائمة النماذج المتاحة"""
        models = {}
        for model_id, model_info in Config.get_available_models().items():
            models[model_id] = {
                "name": model_info.name,
                "description": model_info.description,
                "max_tokens": model_info.max_tokens,
                "provider": model_info.provider,
                "is_free": model_info.is_free
            }
        return models

    def switch_to_optimal_model(self, task_type: str) -> bool:
        """التبديل إلى النموذج الأمثل لنوع المهمة"""
        try:
            optimal_model_id = Config.get_model_by_task_type(task_type)
            if optimal_model_id != self.current_model_info.model_id:
                logger.info(f"التبديل إلى النموذج الأمثل للمهمة '{task_type}': {optimal_model_id}")
                return self.switch_model(optimal_model_id)
            return True
        except Exception as e:
            logger.error(f"فشل في التبديل إلى النموذج الأمثل للمهمة '{task_type}'", e)
            return False
    
    def _generate_content_with_retry(self, prompt: str, max_retries: int = None) -> str:
        """توليد المحتوى مع إعادة المحاولة عند الفشل ودعم تدوير مفاتيح API"""
        # استخدام إعدادات النموذج الحالي
        model_settings = self.model_settings if hasattr(self, 'model_settings') else Config.get_model_settings()

        if max_retries is None:
            max_retries = model_settings.get("max_retries", 3)

        retry_delay = model_settings.get("retry_delay", 5)
        rate_limit_delay_range = model_settings.get("rate_limit_delay", (6, 15))

        for attempt in range(max_retries):
            try:
                start_time = time.time()

                # توليد المحتوى بناءً على نوع النموذج
                if self.current_model_info.provider == "gemini":
                    response = self.model.generate_content(prompt)
                    response_time = time.time() - start_time

                    logger.api_call("Gemini", "generate_content", "success", response_time)
                    return response.text.strip()

                elif self.current_model_info.provider == "openrouter":
                    content = self.openrouter_client.generate_content(
                        prompt=prompt,
                        model_id=self.current_model_info.model_id,
                        temperature=model_settings.get("temperature", 0.7),
                        max_tokens=self.current_model_info.max_tokens
                    )

                    if content:
                        response_time = time.time() - start_time
                        logger.api_call("OpenRouter", "generate_content", "success", response_time)
                        return content
                    else:
                        raise RuntimeError("فشل في الحصول على استجابة من OpenRouter")

                else:
                    raise RuntimeError(f"نوع نموذج غير مدعوم: {self.current_model_info.provider}")

            except Exception as e:
                error_str = str(e).lower()
                logger.warning(f"فشل في المحاولة {attempt + 1} من {max_retries}", {"خطأ": str(e)})

                if attempt < max_retries - 1:
                    # تحديد نوع الخطأ واتخاذ الإجراء المناسب
                    if "rate limit" in error_str or "quota" in error_str or "429" in error_str:
                        # محاولة تدوير مفتاح API
                        logger.info("محاولة تدوير مفتاح API...")
                        try:
                            new_api_key = Config.rotate_api_key(self.current_model_info.provider)
                            if new_api_key and new_api_key.key != self.current_api_key:
                                logger.info(f"تم تدوير مفتاح API إلى: {new_api_key.key[:10]}...{new_api_key.key[-4:]}")
                                self._setup_ai()  # إعادة إعداد النموذج بالمفتاح الجديد
                                delay = random.uniform(5, 10)  # تأخير قصير بعد التدوير
                            else:
                                # لا يوجد مفاتيح أخرى، انتظار أطول
                                delay = random.uniform(60, 120)
                                logger.info(f"لا يوجد مفاتيح API أخرى، انتظار {delay:.1f} ثوانٍ")
                        except Exception as rotate_error:
                            logger.error("فشل في تدوير مفتاح API", rotate_error)
                            delay = random.uniform(60, 120)

                    elif "invalid" in error_str or "unauthorized" in error_str:
                        # مفتاح API غير صالح، محاولة التدوير
                        logger.warning("مفتاح API غير صالح، محاولة التدوير...")
                        try:
                            Config.rotate_api_key(self.current_model_info.provider)
                            self._setup_ai()
                            delay = 2
                        except Exception:
                            delay = retry_delay
                    else:
                        delay = retry_delay

                    time.sleep(delay)
                else:
                    logger.error("فشل في توليد المحتوى بعد جميع المحاولات", e)
                    raise

    # ===== منطق التوليد المقسّم (مرحلتان) =====
    def _generate_stage1_planning(self, keyword: str) -> Dict:
        """توليد المرحلة الأولى: الميتاداتا + هيكل تفصيلي بصيغة JSON قابلة للقراءة."""
        template = self._load_prompt_template("main_article_stage1.txt")
        if not template:
            logger.error("فشل في تحميل قالب مرحلة التخطيط للمقال الرئيسي")
            return {}

        prompt = template.format(keyword=keyword)
        raw = self._generate_content_with_retry(prompt)

        # إزالة أي أسوار أكواد أو نص زائد، ثم تحميل JSON
        try:
            json_text = raw
            start = json_text.find('{')
            end = json_text.rfind('}')
            if start != -1 and end != -1 and end > start:
                json_text = json_text[start:end+1]
            plan = json.loads(json_text)
            return plan
        except Exception as e:
            logger.error("فشل في تحليل JSON لمرحلة التخطيط", e)
            return {}

    def _build_front_matter(self, metadata: Dict) -> str:
        """بناء سطور الميتاداتا وفق الصيغة المطلوبة في الملفات."""
        title = metadata.get("title", "")
        seo_title = metadata.get("seo_title", "")
        meta_description = metadata.get("meta_description", "")
        category = metadata.get("category", "")
        tags_line = metadata.get("tags_line", "")
        keywords_line = metadata.get("keywords_line", "")
        status = metadata.get("status", "draft")

        lines = [
            f"# title: {title}".strip(),
            f"# seo_title: {seo_title}".strip(),
            f"# meta_description: {meta_description}".strip(),
            f"# category: {category}".strip(),
            f"# tags: {tags_line}".strip(),
            f"# keywords: {keywords_line}".strip(),
            f"# status: {status}".strip(),
            "",
            ""
        ]
        return "\n".join(lines)

    def _build_section_prompt(self, keyword: str, metadata: Dict, section: Dict, is_first_section: bool) -> str:
        """بناء برومبت كتابة قسم محدد بالاعتماد على الهيكل."""
        template = self._load_prompt_template("main_article_section.txt")
        if not template:
            logger.error("فشل في تحميل قالب برومبت القسم للمقال الرئيسي")
            return ""

        h2_title = section.get("h2", "").strip()
        h3_list = section.get("h3", []) or []
        h3_joined = "\n".join([f"- {h3.strip()}" for h3 in h3_list if h3 and h3.strip()])

        special_mode = "normal"
        if "الأسئلة الشائعة" in h2_title or "FAQ" in h2_title.upper():
            special_mode = "faq"
        elif "خاتمة" in h2_title:
            special_mode = "conclusion"
        elif "مقدمة" in h2_title:
            special_mode = "intro"

        prompt = template.format(
            keyword=keyword,
            h2_title=h2_title,
            h3_list=h3_joined,
            is_first_section=str(is_first_section).lower(),
            special_mode=special_mode,
            seo_title=metadata.get("seo_title", ""),
            meta_description=metadata.get("meta_description", "")
        )
        return prompt

    def _generate_main_article_chunked(self, keyword: str) -> Tuple[str, List[str], List[str]]:
        """توليد المقال الرئيسي على مرحلتين ثم تجميعه: يعيد (المحتوى النهائي، الكلمات المفتاحية، التاجز)."""
        # التبديل إلى النموذج الأمثل للمقالات الرئيسية
        original_model = self.current_model_info.model_id
        self.switch_to_optimal_model("main_article")

        try:
            plan = self._generate_stage1_planning(keyword)
            if not plan:
                raise RuntimeError("مرحلة التخطيط فشلت")

            metadata = plan.get("metadata", {})
            outline = plan.get("outline", [])
            if not outline:
                raise RuntimeError("لم يتم استلام هيكل الأقسام من مرحلة التخطيط")

            front_matter = self._build_front_matter(metadata)

            sections_html: List[str] = []
            for idx, section in enumerate(outline):
                section_prompt = self._build_section_prompt(keyword, metadata, section, is_first_section=(idx == 0))
                section_html = self._generate_content_with_retry(section_prompt)
                sections_html.append(section_html.strip())

            full_content = front_matter + "\n".join(sections_html).strip() + "\n"

            clean_content, related_keywords, tags = self._clean_and_extract_article(full_content)
            return clean_content, related_keywords, tags

        finally:
            # العودة إلى النموذج الأصلي إذا تم تغييره
            if self.current_model_info.model_id != original_model:
                self.switch_model(original_model)
    
    def _clean_and_extract_article(self, content: str) -> Tuple[str, List[str], List[str]]:
        """تنظيف المحتوى واستخراج المقال والكلمات المفتاحية والتاجز"""
        lines = content.splitlines()
        article_lines = []
        keywords = []
        tags = []
        suggested_keywords = []

        # البحث عن بداية المقال الفعلي (بعد # title:)
        article_started = False
        skip_analysis = True

        for line in lines:
            line = line.strip()

            # تخطي التحليل والكلمات المفتاحية في البداية
            if line.startswith("**تحليل نية الباحث") or line.startswith("**الكلمات المفتاحية"):
                skip_analysis = True
                continue

            # البحث عن بداية المقال الحقيقي
            if line.startswith("# title:"):
                article_started = True
                skip_analysis = False
                article_lines.append(line)
                continue

            # استخراج التاجز من سطر tags
            if line.startswith("# tags:"):
                tags_content = line.split(":", 1)[1].strip()
                parts = tags_content.replace("،", ",").split(",")
                # استخراج التاجز للمقال
                tags = [tag.strip() for tag in parts if tag.strip()]
                article_lines.append(line)
                continue
                
            # استخراج الكلمات المفتاحية الطويلة من سطر keywords إذا وجد
            if line.startswith("# keywords:"):
                keywords_content = line.split(":", 1)[1].strip()
                if not keywords_content.startswith("اقترح"):
                    parts = keywords_content.replace("،", ",").split(",")
                    # استخراج الكلمات المفتاحية الطويلة للمقالات الفرعية
                    suggested_keywords = [kw.strip() for kw in parts if kw.strip()]
                article_lines.append(line)
                continue

            # إضافة الأسطر بعد بداية المقال
            if article_started and not skip_analysis:
                article_lines.append(line)
            elif not skip_analysis and line and not line.startswith("**"):
                # إذا لم نجد # title: ولكن وجدنا محتوى عادي
                article_lines.append(line)

        # تنظيف المقال النهائي
        clean_article = "\n".join(article_lines).strip()

        # استخدام الكلمات المفتاحية المقترحة إذا وجدت
        if suggested_keywords:
            keywords = suggested_keywords
            logger.info(f"تم استخراج {len(keywords)} كلمة مفتاحية طويلة من سطر keywords")
        # إذا لم نجد كلمات مفتاحية مقترحة، نستخدم التاجز كبديل
        elif tags:
            keywords = tags
            logger.info(f"تم استخدام {len(tags)} تاج كبديل للكلمات المفتاحية")
        # إذا لم نجد كلمات مفتاحية أو تاجز، نحاول البحث بطريقة أخرى
        else:
            keywords = self._extract_keywords_from_content(content)
            logger.info(f"تم استخراج {len(keywords)} كلمة مفتاحية بالطريقة القديمة")
            if keywords and not tags:
                tags = self._generate_tags_from_keywords(keywords)

        # إذا لم نجد تاجز، نستخرجها من الكلمات المفتاحية
        if not tags and keywords:
            tags = self._generate_tags_from_keywords(keywords)
            logger.info(f"تم توليد {len(tags)} تاج من الكلمات المفتاحية")

        return clean_article, keywords, tags
        
    def _generate_tags_from_keywords(self, keywords: List[str]) -> List[str]:
        """توليد التاجز من الكلمات المفتاحية"""
        # استخراج كلمات فريدة من الكلمات المفتاحية
        all_words = set()
        
        # قائمة الكلمات التي يجب استبعادها من التاجز
        exclude_words = [
            "كيف", "كيفية", "طريقة", "أفضل", "دليل", "شرح", "خطوات", "للمبتدئين", 
            "من", "في", "على", "إلى", "عن", "مع", "بين", "تحت", "فوق", "خلال",
            "عبر", "بعد", "قبل", "أثناء", "حول", "نحو", "عند", "لدى", "منذ", "حتى",
            "2025", "2026", "2027", "2028", "شامل", "كامل", "سريع", "سهل", "بسيط",
            "مجاني", "احترافي", "متقدم", "أساسي", "مبتدئ", "محترف", "خبير"
        ]
        
        # معالجة كل كلمة مفتاحية
        for keyword in keywords:
            # تقسيم الكلمة المفتاحية إلى كلمات فردية
            words = keyword.split()
            
            # إذا كانت الكلمة المفتاحية قصيرة (1-2 كلمة) وليست في قائمة الاستبعاد، أضفها كاملة كتاج
            if len(words) <= 2 and all(word not in exclude_words for word in words):
                # إضافة الكلمة المفتاحية كاملة كتاج إذا كانت قصيرة
                all_words.add(keyword)
            else:
                # استخراج الكلمات الفردية المهمة من الكلمات المفتاحية الطويلة
                for word in words:
                    if len(word) > 3 and word not in exclude_words:
                        all_words.add(word)
        
        # اختيار أفضل 4-6 كلمات كتاجز
        tags = list(all_words)
        if len(tags) > 6:
            # ترتيب التاجز حسب الطول (الأقصر أولاً) لتفضيل الكلمات الأكثر عمومية
            tags.sort(key=len)
            tags = tags[:6]
        
        return tags

    def _add_internal_links(self, content: str, keyword: str, site_id: str, category_id: int = None) -> str:
        """إضافة الروابط الداخلية للمقال مع التركيز على نفس التصنيف"""
        try:
            posts_manager = PostsManager()
            related_posts = posts_manager.find_related_posts(keyword, site_id, category_id, limit=10)

            if not related_posts:
                logger.info("لم يتم العثور على مشاركات مرتبطة للربط الداخلي")
                return content

            # تسجيل معلومات عن المشاركات المرتبطة
            category_matches = [p for p in related_posts if p.get('category_match', False)]
            if category_matches:
                logger.info(f"تم العثور على {len(category_matches)} مشاركة من نفس التصنيف")
            else:
                logger.info("لا توجد مشاركات من نفس التصنيف، سيتم استخدام المشاركات المرتبطة عموماً")

            # تقسيم المحتوى إلى فقرات
            paragraphs = content.split('\n')
            total_words = len(' '.join(paragraphs).split())

            # حساب عدد الروابط المطلوبة (رابط كل 400 كلمة)
            links_needed = max(1, total_words // 300)
            links_needed = min(links_needed, len(related_posts))

            if links_needed == 0:
                return content

            # العثور على مواضع "اقرأ ايضا:" في المحتوى
            read_also_positions = []
            for i, paragraph in enumerate(paragraphs):
                if "اقرأ ايضا:" in paragraph or "اقرأ أيضا:" in paragraph:
                    read_also_positions.append(i)

            # اختيار المشاركات بذكاء (أولوية للتصنيف المطابق)
            selected_posts = []
            category_matches = [p for p in related_posts if p.get('category_match', False)]
            other_posts = [p for p in related_posts if not p.get('category_match', False)]

            # إضافة المشاركات من نفس التصنيف أولاً
            selected_posts.extend(category_matches[:links_needed])

            # إكمال باقي الروابط من المشاركات الأخرى إذا لزم الأمر
            remaining_slots = links_needed - len(selected_posts)
            if remaining_slots > 0:
                selected_posts.extend(other_posts[:remaining_slots])

            # إضافة الروابط
            links_added = 0

            # توزيع الروابط بالتساوي
            content_sections = len(paragraphs) // (links_needed + 1)

            for i, post in enumerate(selected_posts):
                if links_added >= links_needed:
                    break

                # حساب موضع الإدراج
                insert_position = (i + 1) * content_sections

                # التأكد من عدم تجاوز حدود المحتوى
                if insert_position >= len(paragraphs):
                    insert_position = len(paragraphs) - 1

                # إنشاء رابط HTML مع تمييز المشاركات من نفس التصنيف
                category_indicator = " 🎯" if post.get('category_match', False) else ""
                link_html = f'<p><strong>اقرأ أيضا:</strong> <a href="{post["link"]}" target="_blank" rel="noopener">{post["title"]}</a>{category_indicator}</p>'

                # إدراج الرابط
                paragraphs.insert(insert_position + links_added, link_html)
                links_added += 1

            # إزالة أي "اقرأ ايضا:" فارغة
            cleaned_paragraphs = []
            for paragraph in paragraphs:
                if paragraph.strip() == "<p>اقرأ ايضا: </p>" or paragraph.strip() == "<p>اقرأ أيضا: </p>":
                    continue
                cleaned_paragraphs.append(paragraph)

            result_content = '\n'.join(cleaned_paragraphs)

            # إحصائيات الروابط المضافة
            category_links = len([p for p in selected_posts[:links_added] if p.get('category_match', False)])
            other_links = links_added - category_links

            logger.success(f"تم إضافة {links_added} رابط داخلي للمقال")
            if category_links > 0:
                logger.info(f"  • {category_links} رابط من نفس التصنيف 🎯")
            if other_links > 0:
                logger.info(f"  • {other_links} رابط من تصنيفات أخرى")

            return result_content

        except Exception as e:
            logger.error("فشل في إضافة الروابط الداخلية", e)
            return content

    def _extract_keywords_from_content(self, content: str) -> List[str]:
        """استخراج الكلمات المفتاحية من المحتوى (الطريقة القديمة كاحتياط)"""
        # البحث عن الكلمات المفتاحية في سطر keywords أولاً
        for line in content.splitlines():
            if line.lower().startswith("keywords:") or line.lower().startswith("# keywords:"):
                keywords = line.split(":", 1)[1].strip()
                parts = keywords.replace("،", ",").split(",")
                extracted = [kw.strip() for kw in parts if kw.strip()]
                if extracted:
                    logger.info(f"تم استخراج {len(extracted)} كلمة مفتاحية من سطر keywords")
                    return extracted
        
        # البحث عن الكلمات المفتاحية في سطر tags كبديل
        for line in content.splitlines():
            if line.lower().startswith("tags:") or line.lower().startswith("# tags:"):
                tags = line.split(":", 1)[1].strip()
                parts = tags.replace("،", ",").split(",")
                extracted = [tag.strip() for tag in parts if tag.strip()]
                if extracted:
                    logger.info(f"تم استخراج {len(extracted)} كلمة مفتاحية من سطر tags")
                    return extracted
        
        # البحث عن الكلمات المفتاحية في قسم تحليل نية الباحث
        keyword_section = re.search(r'\*\*الكلمات المفتاحية طويلة الذيل\*\*\s*([\s\S]*?)(?=\*\*|$)', content)
        if keyword_section:
            keyword_text = keyword_section.group(1).strip()
            if keyword_text:
                # استخراج الكلمات المفتاحية من النص
                lines = keyword_text.splitlines()
                extracted = []
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith("*") and not line.startswith("-") and not line.startswith("#"):
                        # تنظيف الكلمة المفتاحية
                        clean_kw = re.sub(r'^\d+\.\s*', '', line).strip()
                        if clean_kw:
                            extracted.append(clean_kw)
                if extracted:
                    logger.info(f"تم استخراج {len(extracted)} كلمة مفتاحية من قسم تحليل نية الباحث")
                    return extracted
        
        logger.warning("لم يتم العثور على كلمات مفتاحية في المحتوى")
        return []
    
    def _save_content(self, content: str, keyword: str, is_main: bool = True, main_keyword: str = None) -> str:
        """حفظ المحتوى في ملف"""
        # إنشاء اسم ملف آمن
        safe_filename = keyword.strip().replace(" ", "-").replace("؟", "") + ".txt"
        
        if is_main:
            filepath = os.path.join(Config.DIRECTORIES["articles"], safe_filename)
        else:
            # إنشاء مجلد فرعي للمقالات الفرعية
            sub_dir = os.path.join(Config.DIRECTORIES["articles"], main_keyword.replace(" ", "-"))
            os.makedirs(sub_dir, exist_ok=True)
            filepath = os.path.join(sub_dir, safe_filename)
        
        # حفظ المحتوى
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)
        
        # حساب عدد الكلمات التقريبي
        word_count = len(content.split())
        
        logger.article_generated(keyword, filepath, word_count)
        return filepath

    def generate_main_article(self, keyword: str, site_id: str = None, category_id: int = None) -> Tuple[str, List[str]]:
        """إنتاج المقال الرئيسي مع التحقق من عدم التكرار"""
        logger.info(f"بدء إنتاج المقال الرئيسي للكلمة المفتاحية: {keyword}")

        # التحقق من وجود المقال مسبقاً
        if self._check_article_exists(keyword, is_main=True):
            logger.warning(f"المقال الرئيسي موجود بالفعل للكلمة المفتاحية: {keyword}")
            # إرجاع المسار والكلمات المفتاحية الموجودة
            safe_filename = keyword.strip().replace(" ", "-").replace("؟", "") + ".txt"
            filepath = os.path.join(Config.DIRECTORIES["articles"], safe_filename)
            related_keywords = self._load_keywords(keyword)
            return filepath, related_keywords

        try:
            # محاولة أولى: توليد مقسّم لتجاوز حدود الطول والـ429
            clean_content, related_keywords, tags = self._generate_main_article_chunked(keyword)

            # إضافة الروابط الداخلية إذا تم تحديد موقع
            if site_id:
                clean_content = self._add_internal_links(clean_content, keyword, site_id, category_id)

            # حفظ المقال المنظف
            filepath = self._save_content(clean_content, keyword, is_main=True)

            # حفظ الكلمات المفتاحية الفرعية
            if related_keywords:
                self._save_keywords(keyword, related_keywords)
                logger.success(f"تم استخراج {len(related_keywords)} كلمة مفتاحية فرعية")
            else:
                logger.warning("لم يتم العثور على كلمات مفتاحية فرعية في المقال")
                
            # حفظ التاجز
            if tags:
                self._save_tags(keyword, tags)
                logger.success(f"تم استخراج {len(tags)} تاج للمقال الرئيسي")
            else:
                logger.warning("لم يتم العثور على تاجز للمقال الرئيسي")

            # حفظ في قاعدة البيانات
            metadata = self._extract_metadata(clean_content)
            word_count = len(clean_content.split())
            db.add_main_article(keyword, metadata, filepath, word_count)

            if related_keywords:
                db.add_keywords(keyword, related_keywords)

            # تحديث الإحصائيات اليومية
            db.update_daily_stats(articles_created=1, total_words=word_count, ai_calls=1)

            return filepath, related_keywords

        except Exception as e:
            logger.error(f"فشل التوليد المقسّم للمقال الرئيسي، سيتم الرجوع للتوليد المباشر: {keyword}", e)
            # رجوع إلى التوليد المباشر كحل أخير
            prompt = self._get_main_article_prompt(keyword)
            raw_content = self._generate_content_with_retry(prompt)
            clean_content, related_keywords, tags = self._clean_and_extract_article(raw_content)

            if site_id:
                clean_content = self._add_internal_links(clean_content, keyword, site_id, category_id)

            filepath = self._save_content(clean_content, keyword, is_main=True)

            if related_keywords:
                self._save_keywords(keyword, related_keywords)
            if tags:
                self._save_tags(keyword, tags)

            metadata = self._extract_metadata(clean_content)
            word_count = len(clean_content.split())
            db.add_main_article(keyword, metadata, filepath, word_count)
            if related_keywords:
                db.add_keywords(keyword, related_keywords)
            db.update_daily_stats(articles_created=1, total_words=word_count, ai_calls=1)

            return filepath, related_keywords

    def _check_article_exists(self, keyword: str, is_main: bool = False, main_keyword: str = None) -> bool:
        """التحقق من وجود مقال بنفس الكلمة المفتاحية"""
        try:
            # التحقق من قاعدة البيانات أولاً
            if is_main:
                article_stats = db.get_article_stats(keyword)
                if article_stats:
                    logger.info(f"المقال الرئيسي موجود بالفعل للكلمة: {keyword}")
                    return True
            else:
                # التحقق من المقالات الفرعية في قاعدة البيانات
                database_instance = db.Database()
                with sqlite3.connect(database_instance.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT id FROM sub_articles WHERE sub_keyword = ?', (keyword,))
                    if cursor.fetchone():
                        logger.info(f"المقال الفرعي موجود بالفعل للكلمة: {keyword}")
                        return True

            # التحقق من وجود الملف
            safe_filename = keyword.strip().replace(" ", "-").replace("؟", "") + ".txt"

            if is_main:
                filepath = os.path.join(Config.DIRECTORIES["articles"], safe_filename)
            else:
                sub_dir = os.path.join(Config.DIRECTORIES["articles"], main_keyword.replace(" ", "-"))
                filepath = os.path.join(sub_dir, safe_filename)

            if os.path.exists(filepath):
                logger.info(f"ملف المقال موجود بالفعل: {filepath}")
                return True

            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود المقال: {keyword}", e)
            return False

    def generate_sub_articles(self, main_keyword: str, sub_keywords: List[str] = None, site_id: str = None, category_id: int = None) -> List[str]:
        """إنتاج المقالات الفرعية مع التحقق من عدم التكرار"""
        if sub_keywords is None:
            sub_keywords = self._load_keywords(main_keyword)

        if not sub_keywords:
            logger.warning(f"لا توجد كلمات مفتاحية فرعية للكلمة: {main_keyword}")
            return []
            
        # تحميل التاجز الرئيسية إن وجدت
        main_tags = self._load_tags(main_keyword)

        # فلترة الكلمات المفتاحية لإزالة المقالات الموجودة
        new_keywords = []
        existing_keywords = []

        for keyword in sub_keywords:
            if self._check_article_exists(keyword, is_main=False, main_keyword=main_keyword):
                existing_keywords.append(keyword)
            else:
                new_keywords.append(keyword)

        if existing_keywords:
            logger.info(f"تم تخطي {len(existing_keywords)} كلمة مفتاحية موجودة بالفعل:")
            for kw in existing_keywords:
                logger.info(f"  - {kw}")

        if not new_keywords:
            logger.info("جميع المقالات الفرعية موجودة بالفعل، لا حاجة لإنتاج مقالات جديدة")
            return []

        logger.info(f"بدء إنتاج {len(new_keywords)} مقال فرعي جديد للكلمة الرئيسية: {main_keyword}")

        # التبديل إلى النموذج الأمثل للمقالات الفرعية
        original_model = self.current_model_info.model_id
        self.switch_to_optimal_model("sub_article")

        generated_files = []

        try:
            for i, sub_keyword in enumerate(new_keywords, 1):
                try:
                    logger.info(f"إنتاج المقال الفرعي {i}/{len(new_keywords)}: {sub_keyword}")

                    # توليد المحتوى
                    prompt = self._get_sub_article_prompt(main_keyword, sub_keyword, new_keywords)
                    raw_content = self._generate_content_with_retry(prompt)

                # تنظيف المحتوى واستخراج التاجز
                clean_content, _, sub_tags = self._clean_and_extract_article(raw_content)

                # إضافة الروابط الداخلية إذا تم تحديد موقع
                if site_id:
                    clean_content = self._add_internal_links(clean_content, sub_keyword, site_id, category_id)

                # حفظ المقال
                filepath = self._save_content(clean_content, sub_keyword, is_main=False, main_keyword=main_keyword)
                generated_files.append(filepath)
                
                # حفظ التاجز للمقال الفرعي
                # إذا لم يتم استخراج تاجز من المقال الفرعي، استخدم التاجز الرئيسية
                if not sub_tags and main_tags:
                    sub_tags = main_tags
                
                if sub_tags:
                    self._save_tags(sub_keyword, sub_tags)
                    logger.success(f"تم استخراج {len(sub_tags)} تاج للمقال الفرعي: {sub_keyword}")

                # حفظ في قاعدة البيانات
                metadata = self._extract_metadata(clean_content)
                word_count = len(clean_content.split())
                db.add_sub_article(main_keyword, sub_keyword, metadata.get('title', ''), filepath, word_count)

                # تحديث الإحصائيات
                db.update_daily_stats(articles_created=1, total_words=word_count, ai_calls=1)

                # تأخير عشوائي لتجنب rate limiting
                if i < len(new_keywords):  # لا نحتاج تأخير بعد آخر مقال
                    delay = random.randint(*Config.get_model_settings()["rate_limit_delay"])
                    logger.info(f"انتظار {delay} ثانية قبل المقال التالي...")
                    time.sleep(delay)

                except Exception as e:
                    logger.error(f"فشل في إنتاج المقال الفرعي: {sub_keyword}", e)
                    continue

        finally:
            # العودة إلى النموذج الأصلي إذا تم تغييره
            if self.current_model_info.model_id != original_model:
                self.switch_model(original_model)

        total_keywords = len(sub_keywords)
        logger.success(f"تم إنتاج {len(generated_files)} مقال فرعي جديد من أصل {total_keywords} كلمة مفتاحية")
        if existing_keywords:
            logger.info(f"تم تخطي {len(existing_keywords)} مقال موجود مسبقاً")
        return generated_files

    def _save_keywords(self, main_keyword: str, keywords: List[str]) -> None:
        """حفظ الكلمات المفتاحية في ملف"""
        safe_filename = main_keyword.strip().replace(" ", "-") + ".txt"
        keywords_path = os.path.join(Config.DIRECTORIES["keywords"], safe_filename)

        with open(keywords_path, "w", encoding="utf-8") as f:
            for keyword in keywords:
                f.write(keyword + "\n")

        logger.info(f"تم حفظ {len(keywords)} كلمة مفتاحية في: {keywords_path}")
    
    def _save_tags(self, main_keyword: str, tags: List[str]) -> None:
        """حفظ التاجز في ملف منفصل"""
        safe_filename = "tags-" + main_keyword.strip().replace(" ", "-") + ".txt"
        tags_path = os.path.join(Config.DIRECTORIES["keywords"], safe_filename)

        with open(tags_path, "w", encoding="utf-8") as f:
            for tag in tags:
                f.write(tag + "\n")

        logger.info(f"تم حفظ {len(tags)} تاج في: {tags_path}")
    
    def _load_tags(self, main_keyword: str) -> List[str]:
        """تحميل التاجز من ملف"""
        safe_filename = "tags-" + main_keyword.strip().replace(" ", "-") + ".txt"
        tags_path = os.path.join(Config.DIRECTORIES["keywords"], safe_filename)

        if not os.path.exists(tags_path):
            logger.warning(f"ملف التاجز غير موجود: {tags_path}")
            return []

        try:
            with open(tags_path, "r", encoding="utf-8") as f:
                tags = [line.strip() for line in f if line.strip()]

            logger.info(f"تم تحميل {len(tags)} تاج من: {tags_path}")
            return tags

        except Exception as e:
            logger.error(f"فشل في تحميل التاجز من: {tags_path}", e)
            return []

    def _load_keywords(self, main_keyword: str) -> List[str]:
        """تحميل الكلمات المفتاحية من ملف"""
        safe_filename = main_keyword.strip().replace(" ", "-") + ".txt"
        keywords_path = os.path.join(Config.DIRECTORIES["keywords"], safe_filename)

        if not os.path.exists(keywords_path):
            logger.warning(f"ملف الكلمات المفتاحية غير موجود: {keywords_path}")
            return []

        try:
            with open(keywords_path, "r", encoding="utf-8") as f:
                keywords = [line.strip() for line in f if line.strip()]

            logger.info(f"تم تحميل {len(keywords)} كلمة مفتاحية من: {keywords_path}")
            return keywords

        except Exception as e:
            logger.error(f"فشل في تحميل الكلمات المفتاحية من: {keywords_path}", e)
            return []

    def get_article_stats(self, keyword: str) -> Dict:
        """الحصول على إحصائيات المقال"""
        safe_filename = keyword.strip().replace(" ", "-") + ".txt"
        filepath = os.path.join(Config.DIRECTORIES["articles"], safe_filename)

        if not os.path.exists(filepath):
            return {"exists": False}

        try:
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()

            # حساب الإحصائيات
            word_count = len(content.split())
            char_count = len(content)
            keyword_count = content.lower().count(keyword.lower())

            # استخراج البيانات الوصفية
            metadata = self._extract_metadata(content)

            return {
                "exists": True,
                "file_path": filepath,
                "word_count": word_count,
                "char_count": char_count,
                "keyword_count": keyword_count,
                "keyword_density": (keyword_count / word_count * 100) if word_count > 0 else 0,
                "metadata": metadata,
                "created_date": datetime.fromtimestamp(os.path.getctime(filepath)).strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"فشل في الحصول على إحصائيات المقال: {keyword}", e)
            return {"exists": True, "error": str(e)}

    def _extract_metadata(self, content: str) -> Dict:
        """استخراج البيانات الوصفية من المحتوى"""
        metadata = {}
        lines = content.splitlines()

        for line in lines:
            if line.startswith("#"):
                parts = line[1:].split(":", 1)
                if len(parts) == 2:
                    key = parts[0].strip().lower()
                    value = parts[1].strip()
                    metadata[key] = value

        return metadata
