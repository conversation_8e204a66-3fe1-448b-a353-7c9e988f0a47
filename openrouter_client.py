"""
عميل OpenRouter API لدعم النماذج المجانية
"""

import requests
import json
import time
from typing import Dict, List, Optional
from config import Config
from logger import logger

class OpenRouterClient:
    """عميل للتفاعل مع OpenRouter API"""
    
    def __init__(self):
        """تهيئة عميل OpenRouter"""
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Content-Type": "application/json",
            "HTTP-Referer": "https://contentmaster.pro",
            "X-Title": "ContentMaster Pro"
        }
    
    def _get_api_key(self) -> Optional[str]:
        """الحصول على مفتاح OpenRouter API"""
        api_key = Config.get_smart_api_key("openrouter")
        if not api_key:
            logger.error("لا يوجد مفتاح OpenRouter API متاح")
            return None
        return api_key
    
    def _make_request(self, endpoint: str, data: Dict) -> Optional[Dict]:
        """إجراء طلب إلى OpenRouter API"""
        api_key = self._get_api_key()
        if not api_key:
            return None
        
        headers = self.headers.copy()
        headers["Authorization"] = f"Bearer {api_key}"
        
        try:
            response = requests.post(
                f"{self.base_url}/{endpoint}",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning("تم تجاوز حد الاستخدام لـ OpenRouter")
                # محاولة تدوير المفتاح
                Config.rotate_api_key("openrouter")
                return None
            else:
                logger.error(f"خطأ في OpenRouter API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error("فشل في الاتصال بـ OpenRouter API", e)
            return None
    
    def generate_content(self, prompt: str, model_id: str, **kwargs) -> Optional[str]:
        """توليد محتوى باستخدام OpenRouter"""
        try:
            # إعداد البيانات للطلب
            data = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 4096),
                "top_p": kwargs.get("top_p", 0.9),
                "stream": False
            }
            
            # إجراء الطلب
            response = self._make_request("chat/completions", data)
            
            if response and "choices" in response:
                content = response["choices"][0]["message"]["content"]
                logger.success(f"تم توليد المحتوى بنجاح باستخدام {model_id}")
                return content.strip()
            else:
                logger.error("فشل في الحصول على استجابة صحيحة من OpenRouter")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في توليد المحتوى باستخدام OpenRouter: {model_id}", e)
            return None
    
    def get_available_models(self) -> List[Dict]:
        """الحصول على قائمة النماذج المتاحة من OpenRouter"""
        try:
            response = requests.get(f"{self.base_url}/models", timeout=30)
            if response.status_code == 200:
                models_data = response.json()
                return models_data.get("data", [])
            else:
                logger.error(f"فشل في جلب قائمة النماذج من OpenRouter: {response.status_code}")
                return []
        except Exception as e:
            logger.error("خطأ في جلب قائمة النماذج من OpenRouter", e)
            return []
    
    def get_free_models(self) -> List[Dict]:
        """الحصول على النماذج المجانية فقط"""
        all_models = self.get_available_models()
        free_models = []
        
        for model in all_models:
            # التحقق من أن النموذج مجاني
            pricing = model.get("pricing", {})
            prompt_price = float(pricing.get("prompt", "1"))
            completion_price = float(pricing.get("completion", "1"))
            
            if prompt_price == 0 and completion_price == 0:
                free_models.append(model)
        
        logger.info(f"تم العثور على {len(free_models)} نموذج مجاني في OpenRouter")
        return free_models
    
    def test_connection(self) -> bool:
        """اختبار الاتصال مع OpenRouter API"""
        try:
            api_key = self._get_api_key()
            if not api_key:
                return False
            
            # اختبار بسيط
            test_data = {
                "model": "microsoft/phi-3-mini-128k-instruct:free",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            
            response = self._make_request("chat/completions", test_data)
            return response is not None
            
        except Exception as e:
            logger.error("فشل في اختبار الاتصال مع OpenRouter", e)
            return False

# إنشاء مثيل عام للاستخدام
openrouter_client = OpenRouterClient()
