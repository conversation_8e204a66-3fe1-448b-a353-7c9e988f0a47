"""
عميل OpenRouter API لدعم النماذج المجانية
"""

import requests
import json
import time
from typing import Dict, List, Optional
from config import Config
from logger import logger

class OpenRouterClient:
    """عميل للتفاعل مع OpenRouter API"""
    
    def __init__(self):
        """تهيئة عميل OpenRouter"""
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Content-Type": "application/json",
            "HTTP-Referer": "https://contentmaster.pro",
            "X-Title": "ContentMaster Pro"
        }
    
    def _get_api_key(self) -> Optional[str]:
        """الحصول على مفتاح OpenRouter API"""
        api_key = Config.get_smart_api_key("openrouter")
        if not api_key:
            logger.error("لا يوجد مفتاح OpenRouter API متاح")
            return None
        return api_key
    
    def _make_request(self, endpoint: str, data: Dict) -> Optional[Dict]:
        """إجراء طلب إلى OpenRouter API"""
        api_key = self._get_api_key()
        if not api_key:
            return None
        
        headers = self.headers.copy()
        headers["Authorization"] = f"Bearer {api_key}"
        
        try:
            response = requests.post(
                f"{self.base_url}/{endpoint}",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning("تم تجاوز حد الاستخدام لـ OpenRouter")
                # محاولة تدوير المفتاح
                Config.rotate_api_key("openrouter")
                return None
            else:
                logger.error(f"خطأ في OpenRouter API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error("فشل في الاتصال بـ OpenRouter API", e)
            return None
    
    def generate_content(self, prompt: str, model_id: str, **kwargs) -> Optional[str]:
        """توليد محتوى باستخدام OpenRouter"""
        try:
            # إعداد البيانات للطلب
            data = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 4096),
                "top_p": kwargs.get("top_p", 0.9),
                "stream": False
            }
            
            # إجراء الطلب
            response = self._make_request("chat/completions", data)
            
            if response and "choices" in response:
                content = response["choices"][0]["message"]["content"]
                logger.success(f"تم توليد المحتوى بنجاح باستخدام {model_id}")
                return content.strip()
            else:
                logger.error("فشل في الحصول على استجابة صحيحة من OpenRouter")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في توليد المحتوى باستخدام OpenRouter: {model_id}", e)
            return None
    
    def get_available_models(self) -> List[Dict]:
        """الحصول على قائمة النماذج المتاحة من OpenRouter"""
        try:
            # جلب النماذج بدون مفتاح API (معلومات عامة)
            response = requests.get("https://openrouter.ai/api/v1/models", timeout=30)
            if response.status_code == 200:
                models_data = response.json()
                logger.info(f"تم جلب {len(models_data.get('data', []))} نموذج من OpenRouter")
                return models_data.get("data", [])
            else:
                logger.error(f"فشل في جلب قائمة النماذج من OpenRouter: {response.status_code}")
                return []
        except Exception as e:
            logger.error("خطأ في جلب قائمة النماذج من OpenRouter", e)
            return []
    
    def get_free_models(self) -> List[Dict]:
        """الحصول على النماذج المجانية فقط"""
        all_models = self.get_available_models()
        free_models = []
        
        for model in all_models:
            # التحقق من أن النموذج مجاني
            pricing = model.get("pricing", {})
            prompt_price = float(pricing.get("prompt", "1"))
            completion_price = float(pricing.get("completion", "1"))
            
            if prompt_price == 0 and completion_price == 0:
                free_models.append(model)
        
        logger.info(f"تم العثور على {len(free_models)} نموذج مجاني في OpenRouter")
        return free_models
    
    def test_connection(self) -> bool:
        """اختبار الاتصال مع OpenRouter API"""
        try:
            api_key = self._get_api_key()
            if not api_key:
                logger.error("لا يوجد مفتاح OpenRouter API")
                return False

            # اختبار بسيط مع نموذج متاح
            test_data = {
                "model": "nousresearch/nous-capybara-7b:free",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }

            response = self._make_request("chat/completions", test_data)
            return response is not None

        except Exception as e:
            logger.error("فشل في اختبار الاتصال مع OpenRouter", e)
            return False

    def test_model(self, model_id: str) -> Dict:
        """اختبار نموذج محدد والحصول على معلوماته"""
        try:
            api_key = self._get_api_key()
            if not api_key:
                return {
                    "available": False,
                    "error": "لا يوجد مفتاح OpenRouter API",
                    "response_time": 0
                }

            # اختبار النموذج
            test_data = {
                "model": model_id,
                "messages": [{"role": "user", "content": "Test"}],
                "max_tokens": 5
            }

            import time
            start_time = time.time()
            response = self._make_request("chat/completions", test_data)
            response_time = time.time() - start_time

            if response and "choices" in response:
                return {
                    "available": True,
                    "response_time": response_time,
                    "sample_response": response["choices"][0]["message"]["content"],
                    "error": None
                }
            else:
                return {
                    "available": False,
                    "error": "لم يتم الحصول على استجابة صحيحة",
                    "response_time": response_time
                }

        except Exception as e:
            return {
                "available": False,
                "error": str(e),
                "response_time": 0
            }

    def generate_image(self, prompt: str, model_id: str = "black-forest-labs/flux-1-schnell:free") -> Dict:
        """إنتاج صورة باستخدام نماذج OpenRouter"""
        try:
            api_key = self._get_api_key()
            if not api_key:
                return {
                    "success": False,
                    "error": "لا يوجد مفتاح OpenRouter API",
                    "image_url": None
                }

            # إعداد البيانات لطلب إنتاج الصورة
            data = {
                "model": model_id,
                "prompt": prompt,
                "n": 1,
                "size": "1024x1024",
                "response_format": "url"
            }

            # إرسال الطلب
            response = self._make_request("images/generations", data)

            if response and "data" in response and len(response["data"]) > 0:
                image_url = response["data"][0].get("url")
                if image_url:
                    return {
                        "success": True,
                        "image_url": image_url,
                        "error": None
                    }

            return {
                "success": False,
                "error": "لم يتم الحصول على رابط الصورة",
                "image_url": None
            }

        except Exception as e:
            logger.error(f"خطأ في إنتاج الصورة: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "image_url": None
            }

# إنشاء مثيل عام للاستخدام
openrouter_client = OpenRouterClient()
