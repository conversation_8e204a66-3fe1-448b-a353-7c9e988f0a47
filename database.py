"""
نظام قاعدة البيانات المحلية
"""
import sqlite3
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import json

from config import Config
from logger import logger

class Database:
    """مدير قاعدة البيانات المحلية"""
    
    def __init__(self):
        """تهيئة قاعدة البيانات"""
        Config.ensure_directories()
        self.db_path = os.path.join(
            Config.DIRECTORIES["database"], 
            Config.DATABASE_SETTINGS["name"]
        )
        self._init_database()
        self.create_tables()  # إضافة استدعاء صريح لإنشاء الجداول
        logger.info(f"تم تهيئة قاعدة البيانات: {self.db_path}")
    
    def _init_database(self) -> None:
        """إنشاء جداول قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول المقالات الرئيسية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS main_articles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        keyword TEXT UNIQUE NOT NULL,
                        title TEXT,
                        seo_title TEXT,
                        meta_description TEXT,
                        category TEXT,
                        word_count INTEGER,
                        keyword_density REAL,
                        file_path TEXT,
                        status TEXT DEFAULT 'draft',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول المقالات الفرعية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sub_articles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        main_keyword TEXT NOT NULL,
                        sub_keyword TEXT NOT NULL,
                        title TEXT,
                        word_count INTEGER,
                        file_path TEXT,
                        status TEXT DEFAULT 'draft',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (main_keyword) REFERENCES main_articles (keyword)
                    )
                ''')
                
                # جدول الكلمات المفتاحية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS keywords (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        main_keyword TEXT NOT NULL,
                        related_keyword TEXT NOT NULL,
                        usage_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (main_keyword) REFERENCES main_articles (keyword)
                    )
                ''')
                
                # جدول المنشورات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS publications (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        article_id INTEGER,
                        article_type TEXT, -- 'main' or 'sub'
                        site_name TEXT NOT NULL,
                        post_url TEXT,
                        published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        status TEXT DEFAULT 'published'
                    )
                ''')
                
                # جدول الإحصائيات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS statistics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE DEFAULT CURRENT_DATE,
                        articles_created INTEGER DEFAULT 0,
                        articles_published INTEGER DEFAULT 0,
                        total_words INTEGER DEFAULT 0,
                        ai_api_calls INTEGER DEFAULT 0
                    )
                ''')
                
                conn.commit()
                logger.success("تم إنشاء جداول قاعدة البيانات بنجاح")
                
        except Exception as e:
            logger.error("فشل في إنشاء قاعدة البيانات", e)
            raise

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات (دالة منفصلة للاستدعاء الصريح)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # التحقق من وجود الجداول وإنشاؤها إذا لم تكن موجودة
                tables_to_check = [
                    'main_articles', 'sub_articles', 'keywords',
                    'statistics', 'internal_links', 'posts'
                ]

                missing_tables = []
                for table in tables_to_check:
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                    if not cursor.fetchone():
                        missing_tables.append(table)

                if missing_tables:
                    logger.warning(f"الجداول المفقودة: {', '.join(missing_tables)}")
                    # إعادة تشغيل إنشاء الجداول
                    self._init_database()
                    logger.success("✅ ✅ تم إنشاء جداول قاعدة البيانات بنجاح")
                else:
                    logger.info("جميع الجداول موجودة")

        except Exception as e:
            logger.error("فشل في إنشاء الجداول", e)

    def add_main_article(self, keyword: str, metadata: Dict, file_path: str, word_count: int) -> int:
        """إضافة مقال رئيسي إلى قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # حساب كثافة الكلمة المفتاحية
                keyword_density = 0
                if word_count > 0:
                    # قراءة المحتوى لحساب التكرار
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().lower()
                            keyword_count = content.count(keyword.lower())
                            keyword_density = (keyword_count / word_count) * 100
                    except:
                        pass
                
                cursor.execute('''
                    INSERT OR REPLACE INTO main_articles 
                    (keyword, title, seo_title, meta_description, category, word_count, 
                     keyword_density, file_path, status, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    keyword,
                    metadata.get('title', ''),
                    metadata.get('seo_title', ''),
                    metadata.get('meta_description', ''),
                    metadata.get('category', ''),
                    word_count,
                    keyword_density,
                    file_path,
                    metadata.get('status', 'draft')
                ))
                
                article_id = cursor.lastrowid
                conn.commit()
                
                logger.success(f"تم إضافة المقال الرئيسي إلى قاعدة البيانات: {keyword}")
                return article_id
                
        except Exception as e:
            logger.error(f"فشل في إضافة المقال الرئيسي: {keyword}", e)
            return 0
    
    def add_sub_article(self, main_keyword: str, sub_keyword: str, title: str, file_path: str, word_count: int) -> int:
        """إضافة مقال فرعي إلى قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO sub_articles 
                    (main_keyword, sub_keyword, title, word_count, file_path)
                    VALUES (?, ?, ?, ?, ?)
                ''', (main_keyword, sub_keyword, title, word_count, file_path))
                
                article_id = cursor.lastrowid
                conn.commit()
                
                logger.debug(f"تم إضافة المقال الفرعي: {sub_keyword}")
                return article_id
                
        except Exception as e:
            logger.error(f"فشل في إضافة المقال الفرعي: {sub_keyword}", e)
            return 0
    
    def add_keywords(self, main_keyword: str, related_keywords: List[str]) -> None:
        """إضافة الكلمات المفتاحية المرتبطة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # حذف الكلمات المفتاحية القديمة
                cursor.execute('DELETE FROM keywords WHERE main_keyword = ?', (main_keyword,))
                
                # إضافة الكلمات المفتاحية الجديدة
                for keyword in related_keywords:
                    cursor.execute('''
                        INSERT INTO keywords (main_keyword, related_keyword)
                        VALUES (?, ?)
                    ''', (main_keyword, keyword))
                
                conn.commit()
                logger.debug(f"تم إضافة {len(related_keywords)} كلمة مفتاحية للمقال: {main_keyword}")
                
        except Exception as e:
            logger.error(f"فشل في إضافة الكلمات المفتاحية: {main_keyword}", e)
    
    def add_publication(self, article_id: int, article_type: str, site_name: str, post_url: str) -> None:
        """تسجيل نشر مقال"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO publications (article_id, article_type, site_name, post_url)
                    VALUES (?, ?, ?, ?)
                ''', (article_id, article_type, site_name, post_url))
                
                conn.commit()
                logger.debug(f"تم تسجيل النشر: {post_url}")
                
        except Exception as e:
            logger.error("فشل في تسجيل النشر", e)
    
    def get_article_stats(self, keyword: str) -> Optional[Dict]:
        """الحصول على إحصائيات مقال"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM main_articles WHERE keyword = ?
                ''', (keyword,))
                
                row = cursor.fetchone()
                if not row:
                    return None
                
                columns = [desc[0] for desc in cursor.description]
                article_data = dict(zip(columns, row))
                
                # الحصول على الكلمات المفتاحية المرتبطة
                cursor.execute('''
                    SELECT related_keyword FROM keywords WHERE main_keyword = ?
                ''', (keyword,))
                
                keywords = [row[0] for row in cursor.fetchall()]
                article_data['related_keywords'] = keywords
                
                # الحصول على المقالات الفرعية
                cursor.execute('''
                    SELECT COUNT(*) FROM sub_articles WHERE main_keyword = ?
                ''', (keyword,))
                
                sub_articles_count = cursor.fetchone()[0]
                article_data['sub_articles_count'] = sub_articles_count
                
                # الحصول على معلومات النشر
                cursor.execute('''
                    SELECT site_name, post_url, published_at 
                    FROM publications 
                    WHERE article_id = ? AND article_type = 'main'
                ''', (article_data['id'],))
                
                publications = cursor.fetchall()
                article_data['publications'] = [
                    {'site': pub[0], 'url': pub[1], 'date': pub[2]} 
                    for pub in publications
                ]
                
                return article_data
                
        except Exception as e:
            logger.error(f"فشل في الحصول على إحصائيات المقال: {keyword}", e)
            return None
    
    def get_dashboard_stats(self) -> Dict:
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # عدد المقالات الرئيسية
                cursor.execute('SELECT COUNT(*) FROM main_articles')
                stats['main_articles'] = cursor.fetchone()[0]
                
                # عدد المقالات الفرعية
                cursor.execute('SELECT COUNT(*) FROM sub_articles')
                stats['sub_articles'] = cursor.fetchone()[0]
                
                # عدد المنشورات
                cursor.execute('SELECT COUNT(*) FROM publications')
                stats['publications'] = cursor.fetchone()[0]
                
                # إجمالي الكلمات
                cursor.execute('SELECT SUM(word_count) FROM main_articles')
                result = cursor.fetchone()[0]
                stats['total_words'] = result if result else 0
                
                # المقالات حسب الحالة
                cursor.execute('''
                    SELECT status, COUNT(*) 
                    FROM main_articles 
                    GROUP BY status
                ''')
                stats['articles_by_status'] = dict(cursor.fetchall())
                
                # المقالات حسب التصنيف
                cursor.execute('''
                    SELECT category, COUNT(*) 
                    FROM main_articles 
                    WHERE category IS NOT NULL AND category != ''
                    GROUP BY category
                ''')
                stats['articles_by_category'] = dict(cursor.fetchall())
                
                return stats
                
        except Exception as e:
            logger.error("فشل في الحصول على إحصائيات لوحة التحكم", e)
            return {}
    
    def update_daily_stats(self, articles_created: int = 0, articles_published: int = 0, 
                          total_words: int = 0, ai_calls: int = 0) -> None:
        """تحديث الإحصائيات اليومية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                today = datetime.now().date()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO statistics 
                    (date, articles_created, articles_published, total_words, ai_api_calls)
                    VALUES (?, 
                        COALESCE((SELECT articles_created FROM statistics WHERE date = ?), 0) + ?,
                        COALESCE((SELECT articles_published FROM statistics WHERE date = ?), 0) + ?,
                        COALESCE((SELECT total_words FROM statistics WHERE date = ?), 0) + ?,
                        COALESCE((SELECT ai_api_calls FROM statistics WHERE date = ?), 0) + ?)
                ''', (today, today, articles_created, today, articles_published, 
                      today, total_words, today, ai_calls))
                
                conn.commit()
                
        except Exception as e:
            logger.error("فشل في تحديث الإحصائيات اليومية", e)

# إنشاء مثيل عام للاستخدام
db = Database()

def initialize():
    """تهيئة قاعدة البيانات"""
    db._init_database()
    logger.success("✅ تم إنشاء جداول قاعدة البيانات بنجاح")

# دوال مساعدة على مستوى الوحدة
def add_main_article(keyword: str, metadata: Dict, file_path: str, word_count: int) -> int:
    """إضافة مقال رئيسي إلى قاعدة البيانات"""
    return db.add_main_article(keyword, metadata, file_path, word_count)

def add_keywords(main_keyword: str, related_keywords: List[str]) -> None:
    """إضافة الكلمات المفتاحية المرتبطة"""
    return db.add_keywords(main_keyword, related_keywords)

def update_daily_stats(articles_created: int = 0, articles_published: int = 0, 
                       total_words: int = 0, ai_calls: int = 0) -> None:
    """تحديث الإحصائيات اليومية"""
    return db.update_daily_stats(articles_created, articles_published, total_words, ai_calls)

def get_article_stats(keyword: str) -> Optional[Dict]:
    """الحصول على إحصائيات مقال"""
    return db.get_article_stats(keyword)

def add_sub_article(main_keyword: str, sub_keyword: str, title: str, file_path: str, word_count: int) -> int:
    """إضافة مقال فرعي إلى قاعدة البيانات"""
    return db.add_sub_article(main_keyword, sub_keyword, title, file_path, word_count)
