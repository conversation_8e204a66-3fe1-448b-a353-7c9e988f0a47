"""
واجهة رسومية بسيطة لـ ContentMaster Pro
"""
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from datetime import datetime
from typing import Optional

from content_manager import ContentManager
from publisher import Publisher
from database import db
from logger import logger
from config import Config

class ContentMasterGUI:
    """واجهة رسومية لإدارة المحتوى"""
    
    def __init__(self):
        """تهيئة الواجهة الرسومية"""
        self.root = tk.Tk()
        self.root.title("ContentMaster Pro - نظام إنتاج المحتوى")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # تهيئة المكونات
        self.content_manager = ContentManager()
        self.publisher = Publisher()
        
        # متغيرات الواجهة
        self.keyword_var = tk.StringVar()
        self.site_var = tk.StringVar()
        self.status_var = tk.StringVar(value="جاهز")

        # متغيرات إدارة المواقع
        self.site_name_var = tk.StringVar()
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.url_var = tk.StringVar()

        # متغير التصنيف
        self.category_var = tk.StringVar()
        
        # متغيرات النماذج
        self.current_model_var = tk.StringVar()
        self.model_temp_var = tk.DoubleVar()
        
        self._create_widgets()
        self._setup_keyboard_shortcuts()
        self._update_model_info()
        logger.info("تم تهيئة الواجهة الرسومية بنجاح")

    def _create_context_menu(self, widget):
        """إنشاء قائمة سياقية للنسخ واللصق"""
        context_menu = tk.Menu(self.root, tearoff=0)

        context_menu.add_command(
            label="نسخ",
            command=lambda: self._copy_text(widget),
            accelerator="Ctrl+C"
        )
        context_menu.add_command(
            label="لصق",
            command=lambda: self._paste_text(widget),
            accelerator="Ctrl+V"
        )
        context_menu.add_command(
            label="قص",
            command=lambda: self._cut_text(widget),
            accelerator="Ctrl+X"
        )
        context_menu.add_separator()
        context_menu.add_command(
            label="تحديد الكل",
            command=lambda: self._select_all(widget),
            accelerator="Ctrl+A"
        )

        def show_context_menu(event):
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        widget.bind("<Button-3>", show_context_menu)  # كليك يمين
        return context_menu

    def _copy_text(self, widget):
        """نسخ النص المحدد"""
        try:
            selected_text = ""

            # للـ Entry widgets
            if isinstance(widget, tk.Entry):
                if widget.selection_present():
                    selected_text = widget.selection_get()

            # للـ Text widgets (بما في ذلك ScrolledText)
            elif hasattr(widget, 'tag_ranges'):
                if widget.tag_ranges(tk.SEL):
                    selected_text = widget.selection_get()

            # للـ Listbox widgets
            elif isinstance(widget, tk.Listbox):
                selection = widget.curselection()
                if selection:
                    selected_text = widget.get(selection[0])

            if selected_text:
                self.root.clipboard_clear()
                self.root.clipboard_append(selected_text)

        except tk.TclError:
            pass  # لا يوجد نص محدد

    def _paste_text(self, widget):
        """لصق النص من الحافظة"""
        try:
            clipboard_text = self.root.clipboard_get()

            # للـ Entry widgets
            if isinstance(widget, tk.Entry):
                if widget.selection_present():
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                widget.insert(tk.INSERT, clipboard_text)

            # للـ Text widgets (بما في ذلك ScrolledText)
            elif hasattr(widget, 'tag_ranges'):
                if widget.tag_ranges(tk.SEL):
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)
                widget.insert(tk.INSERT, clipboard_text)

            # للـ Listbox - لا يدعم اللصق عادة
            elif isinstance(widget, tk.Listbox):
                pass  # Listbox لا يدعم اللصق

        except tk.TclError:
            pass  # الحافظة فارغة أو خطأ في اللصق

    def _cut_text(self, widget):
        """قص النص المحدد"""
        try:
            selected_text = ""

            # للـ Entry widgets
            if isinstance(widget, tk.Entry):
                if widget.selection_present():
                    selected_text = widget.selection_get()
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)

            # للـ Text widgets (بما في ذلك ScrolledText)
            elif hasattr(widget, 'tag_ranges'):
                if widget.tag_ranges(tk.SEL):
                    selected_text = widget.selection_get()
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                    widget.delete(tk.SEL_FIRST, tk.SEL_LAST)

            # للـ Listbox - لا يدعم القص عادة
            elif isinstance(widget, tk.Listbox):
                pass  # Listbox لا يدعم القص

        except tk.TclError:
            pass  # لا يوجد نص محدد

    def _select_all(self, widget):
        """تحديد كل النص"""
        try:
            # للـ Entry widgets
            if isinstance(widget, tk.Entry):
                widget.select_range(0, tk.END)
                widget.icursor(tk.END)

            # للـ Text widgets (بما في ذلك ScrolledText)
            elif hasattr(widget, 'tag_add'):
                widget.tag_add(tk.SEL, "1.0", tk.END)
                widget.mark_set(tk.INSERT, "1.0")
                widget.see(tk.INSERT)

            # للـ Listbox widgets
            elif isinstance(widget, tk.Listbox):
                widget.select_set(0, tk.END)

        except tk.TclError:
            pass

    def _setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح العامة"""
        # اختصارات عامة للنافذة الرئيسية
        self.root.bind('<Control-c>', lambda e: self._handle_global_copy(e))
        self.root.bind('<Control-v>', lambda e: self._handle_global_paste(e))
        self.root.bind('<Control-x>', lambda e: self._handle_global_cut(e))
        self.root.bind('<Control-a>', lambda e: self._handle_global_select_all(e))

    def _handle_global_copy(self, event):
        """معالج النسخ العام"""
        focused_widget = self.root.focus_get()
        if focused_widget:
            self._copy_text(focused_widget)

    def _handle_global_paste(self, event):
        """معالج اللصق العام"""
        focused_widget = self.root.focus_get()
        if focused_widget:
            self._paste_text(focused_widget)

    def _handle_global_cut(self, event):
        """معالج القص العام"""
        focused_widget = self.root.focus_get()
        if focused_widget:
            self._cut_text(focused_widget)

    def _handle_global_select_all(self, event):
        """معالج تحديد الكل العام"""
        focused_widget = self.root.focus_get()
        if focused_widget:
            self._select_all(focused_widget)
    
    def _create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🚀 ContentMaster Pro", 
            font=('Arial', 16, 'bold'),
            fg='white', 
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        # الإطار الرئيسي مع تبويبات
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=5)

        # تبويب المحتوى
        self.content_tab = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.content_tab, text="📝 إنتاج المحتوى")

        # تبويب النماذج
        self.models_tab = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.models_tab, text="🤖 إدارة النماذج")

        # تبويب إدارة المواقع
        self.sites_tab = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.sites_tab, text="🌐 إدارة المواقع")

        # تبويب الروابط الداخلية
        self.links_tab = tk.Frame(self.notebook, bg='#f0f0f0')
        self.notebook.add(self.links_tab, text="🔗 الروابط الداخلية")

        # إنشاء محتوى التبويبات
        self._create_content_tab()
        self._create_models_tab()
        self._create_sites_tab()
        self._create_links_tab()

        # شريط الحالة
        status_frame = tk.Frame(self.root, bg='#34495e', height=25)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            bg='#34495e',
            fg='white',
            font=('Arial', 9)
        )
        self.status_label.pack(side='left', padx=10, pady=2)
    
    def _update_model_info(self):
        """تحديث معلومات النموذج الحالي"""
        try:
            # الحصول على معلومات النموذج الحالي
            model_info = self.content_manager.get_current_model_info()
            
            # تحديث متغيرات الواجهة
            self.current_model_var.set(f"{model_info['name']} ({model_info['id']})")
            self.model_temp_var.set(model_info['temperature'])
            
            # تحديث معلومات النموذج في إطار النموذج الحالي
            info_text = f"🔹 الاسم: {model_info['name']}\n"
            info_text += f"🔹 الوصف: {model_info['description']}\n"
            info_text += f"🔹 درجة الحرارة: {model_info['temperature']}\n"
            info_text += f"🔹 عدد المحاولات: {model_info['max_retries']}\n"
            info_text += f"🔹 تأخير المحاولة: {model_info['retry_delay']} ثوانٍ\n"
            info_text += f"🔹 تأخير حد الاستخدام: {model_info.get('rate_limit_delay', 60)} ثانية"
            
            # تحديث نص معلومات النموذج الحالي
            if hasattr(self, 'current_model_details'):
                self.current_model_details.config(state='normal')
                self.current_model_details.delete(1.0, tk.END)
                self.current_model_details.insert(1.0, info_text)
                self.current_model_details.config(state='disabled')
                
        except Exception as e:
            logger.error("فشل في تحديث معلومات النموذج", e)
            if hasattr(self, 'current_model_details'):
                self.current_model_details.config(state='normal')
                self.current_model_details.delete(1.0, tk.END)
                self.current_model_details.insert(1.0, "❌ فشل في تحميل معلومات النموذج")
                self.current_model_details.config(state='disabled')
    
    def _create_models_tab(self):
        """إنشاء تبويب إدارة النماذج"""
        # إطار النموذج الحالي
        current_frame = tk.LabelFrame(self.models_tab, text="✨ النموذج الحالي", font=('Arial', 10, 'bold'))
        current_frame.pack(fill='x', padx=10, pady=5)
        
        # عرض النموذج الحالي
        header_frame = tk.Frame(current_frame)
        header_frame.pack(fill='x', padx=5, pady=2)
        
        tk.Label(header_frame, text="النموذج المستخدم:", font=('Arial', 9)).pack(side='left', padx=5)
        current_model_label = tk.Label(header_frame, textvariable=self.current_model_var, font=('Arial', 9, 'bold'), fg='#2c3e50')
        current_model_label.pack(side='left', padx=5)
        
        # إطار تفاصيل النموذج الحالي
        details_frame = tk.Frame(current_frame)
        details_frame.pack(fill='x', padx=5, pady=5)
        
        # مربع نص لعرض معلومات النموذج الحالي
        self.current_model_details = tk.Text(details_frame, height=6, font=('Arial', 8), wrap=tk.WORD)
        self.current_model_details.pack(side='left', fill='both', expand=True)
        self.current_model_details.config(state='disabled')
        
        # شريط تمرير لمربع النص
        details_scrollbar = tk.Scrollbar(details_frame, orient='vertical', command=self.current_model_details.yview)
        details_scrollbar.pack(side='right', fill='y')
        self.current_model_details.config(yscrollcommand=details_scrollbar.set)
        
        # إطار اختيار النموذج
        selection_frame = tk.LabelFrame(self.models_tab, text="🔄 تبديل النموذج", font=('Arial', 10, 'bold'))
        selection_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # تعليمات استخدام
        instructions_frame = tk.Frame(selection_frame)
        instructions_frame.pack(fill='x', padx=5, pady=2)
        
        tk.Label(
            instructions_frame, 
            text="اختر نموذجًا من القائمة أدناه ثم انقر على زر 'تبديل النموذج' للتغيير", 
            font=('Arial', 8), 
            fg='#7f8c8d'
        ).pack(fill='x')
        
        # قائمة النماذج المتاحة
        models_list_frame = tk.Frame(selection_frame)
        models_list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # إنشاء Listbox للنماذج
        self.models_listbox = tk.Listbox(models_list_frame, height=8, font=('Arial', 9))
        models_scrollbar = tk.Scrollbar(models_list_frame, orient='vertical', command=self.models_listbox.yview)
        self.models_listbox.configure(yscrollcommand=models_scrollbar.set)
        
        self.models_listbox.pack(side='left', fill='both', expand=True)
        models_scrollbar.pack(side='right', fill='y')
        
        # إطار معلومات النموذج المحدد
        info_frame = tk.LabelFrame(selection_frame, text="📋 معلومات النموذج المحدد", font=('Arial', 9, 'bold'))
        info_frame.pack(fill='x', padx=5, pady=5)
        
        # إطار نص المعلومات مع شريط التمرير
        info_text_frame = tk.Frame(info_frame)
        info_text_frame.pack(fill='x', padx=5, pady=5)
        
        self.model_info_text = tk.Text(info_text_frame, height=5, font=('Arial', 8), wrap=tk.WORD, state='disabled')
        self.model_info_text.pack(side='left', fill='both', expand=True)
        
        # شريط التمرير لنص المعلومات
        info_scrollbar = tk.Scrollbar(info_text_frame, orient='vertical', command=self.model_info_text.yview)
        info_scrollbar.pack(side='right', fill='y')
        self.model_info_text.config(yscrollcommand=info_scrollbar.set)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(selection_frame)
        buttons_frame.pack(fill='x', padx=5, pady=5)
        
        switch_btn = tk.Button(
            buttons_frame,
            text="🔄 تبديل النموذج",
            command=self._switch_model,
            bg='#3498db',
            fg='white',
            font=('Arial', 9, 'bold'),
            relief='flat',
            padx=20
        )
        switch_btn.pack(side='left', padx=5)
        
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث القائمة",
            command=self._refresh_models_list,
            bg='#95a5a6',
            fg='white',
            font=('Arial', 9, 'bold'),
            relief='flat',
            padx=20
        )
        refresh_btn.pack(side='left', padx=5)
        
        # إطار إعدادات النموذج
        settings_frame = tk.LabelFrame(self.models_tab, text="⚙️ إعدادات النموذج", font=('Arial', 10, 'bold'))
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # إعدادات درجة الحرارة
        temp_frame = tk.Frame(settings_frame)
        temp_frame.pack(fill='x', padx=5, pady=5)
        
        temp_label = tk.Label(temp_frame, text="درجة الحرارة:", font=('Arial', 9))
        temp_label.pack(side='left', padx=5)
        
        # شريط تمرير لضبط درجة الحرارة
        temp_scale = tk.Scale(
            temp_frame, 
            from_=0.0, 
            to=1.0, 
            orient='horizontal',
            resolution=0.01,
            variable=self.model_temp_var,
            length=200,
            showvalue=True,
            digits=2,
            command=self._on_temp_change
        )
        temp_scale.pack(side='left', fill='x', expand=True, padx=5)
        
        # وصف درجة الحرارة
        temp_desc_frame = tk.Frame(settings_frame)
        temp_desc_frame.pack(fill='x', padx=5, pady=2)
        
        tk.Label(
            temp_desc_frame, 
            text="0.0: أكثر تحديدًا وثباتًا | 1.0: أكثر إبداعًا وتنوعًا", 
            font=('Arial', 8), 
            fg='#7f8c8d'
        ).pack(fill='x')
        
        # زر حفظ الإعدادات
        save_btn = tk.Button(
            settings_frame,
            text="💾 حفظ الإعدادات",
            command=self._save_model_settings,
            bg='#27ae60',
            fg='white',
            font=('Arial', 9, 'bold'),
            relief='flat',
            padx=20
        )
        save_btn.pack(side='right', padx=5, pady=5)
        
        # ربط الأحداث
        self.models_listbox.bind('<<ListboxSelect>>', self._on_model_select)
        
        # تحميل النماذج المتاحة
        self._refresh_models_list()
        
    def _refresh_models_list(self):
        """تحديث قائمة النماذج المتاحة"""
        try:
            self.models_listbox.delete(0, tk.END)
            self.available_models = self.content_manager.get_available_models()
            
            # الحصول على معلومات النموذج الحالي
            current_model = self.content_manager.get_current_model_info()
            current_model_id = current_model['id']
            
            # إضافة النماذج إلى القائمة
            for model_id, model_info in self.available_models.items():
                # إضافة علامة ✓ للنموذج الحالي
                if model_id == current_model_id:
                    display_text = f"✓ {model_info['name']} ({model_id})"
                else:
                    display_text = f"{model_info['name']} ({model_id})"
                    
                self.models_listbox.insert(tk.END, display_text)
                
            # تحديد النموذج الحالي في القائمة
            for i, (model_id, _) in enumerate(self.available_models.items()):
                if model_id == current_model_id:
                    self.models_listbox.selection_set(i)
                    self.models_listbox.see(i)
                    # تمييز النموذج الحالي بلون مختلف
                    self.models_listbox.itemconfig(i, {'bg': '#e6f7ff', 'fg': '#0066cc'})
                    break
            
            # تحديث معلومات النموذج المحدد
            self._on_model_select(None)
                    
        except Exception as e:
            logger.error("فشل في تحديث قائمة النماذج", e)
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة النماذج: {str(e)}")
    
    def _on_model_select(self, event):
        """معالج اختيار نموذج من القائمة"""
        try:
            selection = self.models_listbox.curselection()
            if not selection:
                return
                
            index = selection[0]
            model_ids = list(self.available_models.keys())
            if index < len(model_ids):
                model_id = model_ids[index]
                model_info = self.available_models[model_id]
                
                # الحصول على إعدادات النموذج المحدد
                try:
                    model_settings = Config.get_model_specific_settings(model_id)
                    
                    # عرض معلومات النموذج المفصلة
                    info_text = f"📝 الوصف: {model_info['description']}\n\n"
                    info_text += f"🔢 الحد الأقصى للرموز: {model_info['max_tokens']:,}\n"
                    info_text += f"🌡️ درجة الحرارة: {model_settings.get('temperature', 0.7)}\n"
                    info_text += f"🔄 عدد المحاولات: {model_settings.get('max_retries', 3)}\n"
                    info_text += f"⏱️ تأخير المحاولة: {model_settings.get('retry_delay', 5)} ثوانٍ\n\n"
                    info_text += f"🆔 المعرف: {model_id}"
                    
                except Exception:
                    # إذا فشل الحصول على الإعدادات المفصلة، عرض المعلومات الأساسية
                    info_text = f"📝 الوصف: {model_info['description']}\n\n"
                    info_text += f"🔢 الحد الأقصى للرموز: {model_info['max_tokens']:,}\n"
                    info_text += f"🆔 المعرف: {model_id}"
                
                # تحديث نص المعلومات
                self.model_info_text.config(state='normal')
                self.model_info_text.delete(1.0, tk.END)
                self.model_info_text.insert(1.0, info_text)
                self.model_info_text.config(state='disabled')
                
                # تحديد ما إذا كان هذا هو النموذج الحالي
                current_model = self.content_manager.get_current_model_info()
                if model_id == current_model['id']:
                    # تمييز النموذج الحالي بلون مختلف
                    self.models_listbox.itemconfig(index, {'bg': '#e6f7ff', 'fg': '#0066cc'})
                
        except Exception as e:
            logger.error("فشل في عرض معلومات النموذج", e)
    
    def _switch_model(self):
        """تبديل النموذج المحدد"""
        try:
            selection = self.models_listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار نموذج من القائمة")
                return
                
            index = selection[0]
            model_ids = list(self.available_models.keys())
            if index < len(model_ids):
                model_id = model_ids[index]
                model_info = self.available_models[model_id]
                
                # الحصول على إعدادات النموذج المحدد
                try:
                    model_settings = Config.get_model_specific_settings(model_id)
                    
                    # تأكيد التبديل مع عرض معلومات مفصلة
                    result = messagebox.askyesno(
                        "تأكيد التبديل",
                        f"هل تريد تبديل النموذج إلى:\n{model_info['name']}؟\n\n" +
                        f"• الوصف: {model_info['description']}\n" +
                        f"• الحد الأقصى للرموز: {model_info['max_tokens']:,}\n" +
                        f"• درجة الحرارة: {model_settings.get('temperature', 0.7)}\n" +
                        f"• عدد المحاولات: {model_settings.get('max_retries', 3)}\n" +
                        f"• تأخير المحاولة: {model_settings.get('retry_delay', 5)} ثوانٍ"
                    )
                except Exception:
                    # إذا فشل الحصول على الإعدادات المفصلة، عرض المعلومات الأساسية
                    result = messagebox.askyesno(
                        "تأكيد التبديل",
                        f"هل تريد تبديل النموذج إلى:\n{model_info['name']}؟\n\n" +
                        f"• الوصف: {model_info['description']}\n" +
                        f"• الحد الأقصى للرموز: {model_info['max_tokens']:,}"
                    )
                
                if result:
                    self.status_var.set("⏳ جاري تبديل النموذج...")
                    self.root.update()
                    
                    if self.content_manager.switch_model(model_id):
                        # تحديث معلومات النموذج في الواجهة
                        self._update_model_info()
                        
                        # الحصول على معلومات النموذج المحدثة
                        updated_info = self.content_manager.get_current_model_info()
                        
                        # عرض رسالة نجاح مع معلومات إضافية
                        success_message = f"تم تبديل النموذج بنجاح إلى:\n{model_info['name']}\n\n"
                        success_message += f"• الوصف: {model_info['description']}\n"
                        success_message += f"• درجة الحرارة: {updated_info['temperature']}\n"
                        success_message += f"• عدد المحاولات: {updated_info['max_retries']}\n"
                        success_message += f"• تأخير المحاولة: {updated_info['retry_delay']} ثوانٍ\n"
                        success_message += f"• معدل تأخير حد الاستخدام: {updated_info.get('rate_limit_delay', 60)} ثانية"
                        
                        self.status_var.set(f"✅ تم تبديل النموذج إلى: {model_info['name']}")
                        messagebox.showinfo("نجح", success_message)
                        
                        # تحديث قائمة النماذج
                        self._refresh_models_list()
                    else:
                        self.status_var.set("❌ فشل في تبديل النموذج")
                        messagebox.showerror("خطأ", "فشل في تبديل النموذج")
                        
        except Exception as e:
            logger.error("فشل في تبديل النموذج", e)
            messagebox.showerror("خطأ", f"فشل في تبديل النموذج: {str(e)}")
            self.status_var.set("❌ فشل في تبديل النموذج")
        
    def _on_temp_change(self, value):
        """معالج تغيير قيمة درجة الحرارة"""
        # تحديث حالة الواجهة لإظهار أن هناك تغييرات غير محفوظة
        try:
            # تحويل القيمة إلى رقم عشري
            temp_value = float(value)
            
            # الحصول على النموذج الحالي
            current_model = self.content_manager.get_current_model_info()
            current_temp = current_model.get('temperature', 0.7)
            
            # التحقق مما إذا كانت القيمة قد تغيرت
            if abs(temp_value - current_temp) > 0.001:
                # تحديث حالة الواجهة
                self.status_var.set(f"⚠️ تغييرات غير محفوظة في درجة الحرارة: {temp_value}")
        except Exception as e:
            logger.error("خطأ في معالجة تغيير درجة الحرارة", e)
    
    def _save_model_settings(self):
        """حفظ إعدادات النموذج الحالي"""
        try:
            # الحصول على النموذج الحالي
            current_model = self.content_manager.get_current_model_info()
            model_id = current_model['id']
            
            # الحصول على القيمة الحالية لدرجة الحرارة
            temperature = self.model_temp_var.get()
            
            # تحديث إعدادات النموذج
            settings = {
                'temperature': temperature
            }
            
            # حفظ الإعدادات
            Config.update_model_settings(model_id=model_id, settings=settings)
            
            # تحديث معلومات النموذج في الواجهة
            self._update_model_info()
            
            # تحديث معلومات النموذج المحدد في القائمة
            self._on_model_select(None)
            
            # عرض رسالة نجاح
            self.status_var.set(f"✅ تم حفظ إعدادات النموذج: {current_model['name']}")
            messagebox.showinfo("نجح", f"تم حفظ إعدادات النموذج بنجاح\n\n• درجة الحرارة: {temperature}")
            
        except Exception as e:
            logger.error("فشل في حفظ إعدادات النموذج", e)
            messagebox.showerror("خطأ", f"فشل في حفظ إعدادات النموذج: {str(e)}")
            self.status_var.set("❌ فشل في حفظ إعدادات النموذج")
    
    def _create_content_tab(self):
        """إنشاء تبويب المحتوى"""
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.content_tab, text="إدخال البيانات", font=('Arial', 10, 'bold'))
        input_frame.pack(fill='x', pady=5, padx=5)

        # حقل الكلمة المفتاحية
        tk.Label(input_frame, text="الكلمة المفتاحية:", font=('Arial', 9)).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        keyword_entry = tk.Entry(input_frame, textvariable=self.keyword_var, font=('Arial', 9), width=40)
        keyword_entry.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        self._create_context_menu(keyword_entry)

        # اختيار الموقع
        tk.Label(input_frame, text="الموقع:", font=('Arial', 9)).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.site_combo = ttk.Combobox(input_frame, textvariable=self.site_var, state="readonly")
        self.site_combo.grid(row=1, column=1, padx=5, pady=5, sticky='w')
        self.site_combo.bind('<<ComboboxSelected>>', self._on_site_changed)

        # اختيار التصنيف
        tk.Label(input_frame, text="التصنيف:", font=('Arial', 9)).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.category_combo = ttk.Combobox(input_frame, textvariable=self.category_var, state="readonly")
        self.category_combo.grid(row=2, column=1, padx=5, pady=5, sticky='w')

        # زر جلب التصنيفات
        fetch_categories_btn = tk.Button(
            input_frame,
            text="🔄 جلب التصنيفات",
            command=self._fetch_categories,
            bg='#3498db',
            fg='white',
            font=('Arial', 8)
        )
        fetch_categories_btn.grid(row=2, column=2, padx=5, pady=5)

        input_frame.columnconfigure(1, weight=1)
        
        # إطار الأزرار
        buttons_frame = tk.LabelFrame(self.content_tab, text="العمليات", font=('Arial', 10, 'bold'))
        buttons_frame.pack(fill='x', pady=5, padx=5)
        
        # الصف الأول من الأزرار
        row1_frame = tk.Frame(buttons_frame)
        row1_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(
            row1_frame, 
            text="📝 إنشاء مقال رئيسي", 
            command=self._create_main_article,
            bg='#3498db', 
            fg='white', 
            font=('Arial', 9, 'bold'),
            width=20
        ).pack(side='left', padx=2)
        
        tk.Button(
            row1_frame, 
            text="📚 إنشاء مقالات فرعية", 
            command=self._create_sub_articles,
            bg='#9b59b6', 
            fg='white', 
            font=('Arial', 9, 'bold'),
            width=20
        ).pack(side='left', padx=2)
        
        tk.Button(
            row1_frame, 
            text="🌐 نشر مقال واحد", 
            command=self._publish_single,
            bg='#27ae60', 
            fg='white', 
            font=('Arial', 9, 'bold'),
            width=20
        ).pack(side='left', padx=2)
        
        # الصف الثاني من الأزرار
        row2_frame = tk.Frame(buttons_frame)
        row2_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(
            row2_frame, 
            text="🌐 نشر جميع المقالات", 
            command=self._publish_all,
            bg='#e67e22', 
            fg='white', 
            font=('Arial', 9, 'bold'),
            width=20
        ).pack(side='left', padx=2)
        
        tk.Button(
            row2_frame, 
            text="📊 إحصائيات مقال", 
            command=self._show_stats,
            bg='#34495e', 
            fg='white', 
            font=('Arial', 9, 'bold'),
            width=20
        ).pack(side='left', padx=2)
        
        tk.Button(
            row2_frame, 
            text="📈 لوحة التحكم", 
            command=self._show_dashboard,
            bg='#8e44ad', 
            fg='white', 
            font=('Arial', 9, 'bold'),
            width=20
        ).pack(side='left', padx=2)
        
        # إطار السجل
        log_frame = tk.LabelFrame(self.content_tab, text="سجل العمليات", font=('Arial', 10, 'bold'))
        log_frame.pack(fill='both', expand=True, pady=5, padx=5)
        
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=15,
            font=('Consolas', 9),
            bg='#2c3e50',
            fg='#ecf0f1',
            insertbackground='white'
        )
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        self._create_context_menu(self.log_text)

        # تحديث قائمة المواقع بعد إنشاء site_combo
        self._update_sites_list()

        # إضافة رسالة ترحيب
        self._log_message("🚀 مرحباً بك في ContentMaster Pro")
        self._log_message("💡 أدخل كلمة مفتاحية واختر العملية المطلوبة")

    def _update_sites_list(self):
        """تحديث قائمة المواقع في الـ combobox"""
        sites = Config.get_site_ids()
        if sites:
            self.site_combo['values'] = sites
            if not self.site_var.get():
                self.site_var.set(sites[0])
        else:
            self.site_combo['values'] = []
            self.site_var.set("")

        # تحديث تبويب الروابط أيضاً (إذا كان موجوداً)
        self._update_links_sites_list()

        # تحديث التصنيفات للموقع الحالي
        self._update_categories_list()

    def _update_all_sites_lists(self):
        """تحديث جميع قوائم المواقع في التطبيق"""
        self._update_sites_list()
        self._update_links_sites_list()

    def _on_site_changed(self, event=None):
        """عند تغيير الموقع المحدد"""
        self._update_categories_list()

    def _update_categories_list(self):
        """تحديث قائمة التصنيفات للموقع المحدد"""
        site_id = self.site_var.get()
        if not site_id:
            self.category_combo['values'] = []
            self.category_var.set("")
            return

        try:
            from posts_manager import PostsManager
            posts_manager = PostsManager()
            categories = posts_manager.get_stored_categories(site_id)

            if categories:
                category_names = [f"{cat['name']} (ID: {cat['category_id']})" for cat in categories]
                self.category_combo['values'] = category_names
                if not self.category_var.get() and category_names:
                    self.category_var.set(category_names[0])
            else:
                self.category_combo['values'] = []
                self.category_var.set("")

        except Exception as e:
            logger.error(f"فشل في تحديث قائمة التصنيفات: {str(e)}")
            self.category_combo['values'] = []
            self.category_var.set("")

    def _fetch_categories(self):
        """جلب التصنيفات من الموقع المحدد"""
        site_id = self.site_var.get()
        if not site_id:
            messagebox.showerror("خطأ", "يرجى اختيار موقع أولاً")
            return

        self._log_message(f"🔄 بدء جلب التصنيفات من الموقع: {site_id}")

        def fetch_in_background():
            try:
                from posts_manager import PostsManager
                posts_manager = PostsManager()

                # جلب التصنيفات
                categories = posts_manager.fetch_categories(site_id)

                # تحديث الواجهة في الخيط الرئيسي
                self.root.after(0, lambda: self._on_categories_fetched(categories))

            except Exception as e:
                error_msg = f"❌ فشل في جلب التصنيفات: {str(e)}"
                self.root.after(0, lambda: self._log_message(error_msg, "ERROR"))

        # تشغيل العملية في خيط منفصل
        thread = threading.Thread(target=fetch_in_background)
        thread.daemon = True
        thread.start()

    def _on_categories_fetched(self, categories):
        """معالجة نتيجة جلب التصنيفات"""
        self._update_categories_list()
        self._log_message(f"✅ تم جلب {len(categories)} تصنيف بنجاح", "SUCCESS")

    def _refresh_sites_list(self):
        """تحديث قائمة المواقع في الـ listbox"""
        self.sites_listbox.delete(0, tk.END)
        for site_id, config in Config.get_wordpress_sites().items():
            display_text = f"{site_id} - {config['name']} ({config['site_url']})"
            self.sites_listbox.insert(tk.END, display_text)

    def _on_site_select(self, event):
        """عند اختيار موقع من القائمة"""
        selection = self.sites_listbox.curselection()
        if selection:
            site_id = list(Config.get_all_sites().keys())[selection[0]]
            site_config = Config.get_wordpress_site(site_id)

            # ملء الحقول ببيانات الموقع المختار
            self.site_name_var.set(site_config.get('name', ''))
            self.username_var.set(site_config.get('username', ''))
            self.password_var.set(site_config.get('app_password', ''))
            self.url_var.set(site_config.get('site_url', ''))

    def _add_site(self):
        """إضافة موقع جديد"""
        try:
            name = self.site_name_var.get().strip()
            username = self.username_var.get().strip()
            password = self.password_var.get().strip()
            url = self.url_var.get().strip()

            if not all([name, username, password, url]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            # إنشاء معرف فريد للموقع
            site_id = name.lower().replace(" ", "_")

            # التحقق من عدم وجود الموقع
            if site_id in Config.get_all_sites():
                messagebox.showerror("خطأ", "موقع بهذا الاسم موجود بالفعل")
                return

            # إضافة الموقع
            Config.add_wordpress_site(site_id, name, username, password, url)

            # تحديث القوائم
            self._refresh_sites_list()
            self._update_all_sites_lists()

            # مسح الحقول
            self._clear_site_fields()

            self._log_message(f"✅ تم إضافة الموقع: {name}", "SUCCESS")

        except Exception as e:
            self._log_message(f"❌ فشل في إضافة الموقع: {str(e)}", "ERROR")
            logger.error("خطأ في إضافة موقع", e)

    def _update_site(self):
        """تحديث موقع موجود"""
        try:
            selection = self.sites_listbox.curselection()
            if not selection:
                messagebox.showerror("خطأ", "يرجى اختيار موقع من القائمة")
                return

            site_id = list(Config.get_all_sites().keys())[selection[0]]

            name = self.site_name_var.get().strip()
            username = self.username_var.get().strip()
            password = self.password_var.get().strip()
            url = self.url_var.get().strip()

            if not all([name, username, password, url]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            # تحديث الموقع
            Config.update_wordpress_site(
                site_id,
                name=name,
                username=username,
                app_password=password,
                site_url=url
            )

            # تحديث القوائم
            self._refresh_sites_list()
            self._update_all_sites_lists()

            self._log_message(f"✅ تم تحديث الموقع: {name}", "SUCCESS")

        except Exception as e:
            self._log_message(f"❌ فشل في تحديث الموقع: {str(e)}", "ERROR")
            logger.error("خطأ في تحديث موقع", e)

    def _delete_site(self):
        """حذف موقع"""
        try:
            selection = self.sites_listbox.curselection()
            if not selection:
                messagebox.showerror("خطأ", "يرجى اختيار موقع من القائمة")
                return

            site_id = list(Config.get_all_sites().keys())[selection[0]]
            site_config = Config.get_wordpress_site(site_id)

            # تأكيد الحذف
            result = messagebox.askyesno(
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الموقع '{site_config['name']}'؟"
            )

            if result:
                Config.remove_wordpress_site(site_id)

                # تحديث القوائم
                self._refresh_sites_list()
                self._update_all_sites_lists()

                # مسح الحقول
                self._clear_site_fields()

                self._log_message(f"✅ تم حذف الموقع: {site_config['name']}", "SUCCESS")

        except Exception as e:
            self._log_message(f"❌ فشل في حذف الموقع: {str(e)}", "ERROR")
            logger.error("خطأ في حذف موقع", e)

    def _clear_site_fields(self):
        """مسح حقول إدخال بيانات الموقع"""
        self.site_name_var.set("")
        self.username_var.set("")
        self.password_var.set("")
        self.url_var.set("")

    def _update_links_sites_list(self):
        """تحديث قائمة المواقع في تبويب الروابط"""
        # التحقق من وجود العنصر قبل التحديث
        if not hasattr(self, 'links_site_combo'):
            return

        sites = Config.get_site_ids()
        if sites:
            self.links_site_combo['values'] = sites
            if not self.links_site_combo.get():
                self.links_site_combo.set(sites[0])
        else:
            self.links_site_combo['values'] = []

    def _fetch_posts_incremental(self):
        """جلب المشاركات الجديدة فقط"""
        site_id = self.links_site_combo.get()
        if not site_id:
            messagebox.showerror("خطأ", "يرجى اختيار موقع")
            return

        self._log_message("🔄 بدء جلب المشاركات الجديدة...")

        def fetch_in_background():
            try:
                from posts_manager import PostsManager
                posts_manager = PostsManager()

                # عرض إحصائيات المزامنة
                stats = posts_manager.get_sync_stats(site_id)
                if stats['is_synced']:
                    self.root.after(0, lambda: self._log_message(f"📊 آخر مزامنة: {stats['last_sync']}", "INFO"))
                    self.root.after(0, lambda: self._log_message(f"📊 المشاركات المحفوظة: {stats['total_stored']}", "INFO"))
                    self.root.after(0, lambda: self._log_message("🔍 البحث عن مشاركات جديدة فقط...", "INFO"))
                else:
                    self.root.after(0, lambda: self._log_message("⚠️ لم يتم تزامن الموقع من قبل، استخدم 'جلب كامل' أولاً", "WARNING"))

                # جلب المشاركات الجديدة
                posts = posts_manager.fetch_all_posts(site_id, incremental=True)

                # تحديث الواجهة في الخيط الرئيسي
                self.root.after(0, lambda: self._update_posts_display_incremental(posts, stats['total_stored']))

            except Exception as e:
                error_msg = f"❌ فشل في جلب المشاركات: {str(e)}"
                self.root.after(0, lambda: self._log_message(error_msg, "ERROR"))

        # تشغيل العملية في خيط منفصل
        thread = threading.Thread(target=fetch_in_background)
        thread.daemon = True
        thread.start()

    def _fetch_posts_full(self):
        """جلب جميع المشاركات (إعادة تعيين كاملة)"""
        site_id = self.links_site_combo.get()
        if not site_id:
            messagebox.showerror("خطأ", "يرجى اختيار موقع")
            return

        result = messagebox.askyesno(
            "تأكيد الجلب الكامل",
            "هل تريد إعادة جلب جميع المشاركات؟\nسيتم مسح البيانات الحالية وجلب كل شيء من جديد."
        )

        if not result:
            return

        self._log_message("🔄 بدء الجلب الكامل للمشاركات...")

        def fetch_in_background():
            try:
                from posts_manager import PostsManager
                posts_manager = PostsManager()

                # إعادة تعيين حالة المزامنة
                posts_manager.reset_sync_status(site_id)

                # جلب جميع المشاركات
                posts = posts_manager.fetch_all_posts(site_id, incremental=False)

                # تحديث الواجهة في الخيط الرئيسي
                self.root.after(0, lambda: self._update_posts_display(posts))

            except Exception as e:
                error_msg = f"❌ فشل في الجلب الكامل: {str(e)}"
                self.root.after(0, lambda: self._log_message(error_msg, "ERROR"))

        # تشغيل العملية في خيط منفصل
        thread = threading.Thread(target=fetch_in_background)
        thread.daemon = True
        thread.start()

    def _update_posts_display(self, posts):
        """تحديث عرض المشاركات في الجدول (جلب كامل)"""
        # مسح البيانات الحالية
        for item in self.posts_tree.get_children():
            self.posts_tree.delete(item)

        # إضافة المشاركات الجديدة
        for post in posts:
            self.posts_tree.insert('', 'end', values=(
                post.get('title', ''),
                post.get('link', ''),
                post.get('date', ''),
                post.get('category', '')
            ))

        self._log_message(f"✅ تم جلب {len(posts)} مشاركة بنجاح", "SUCCESS")

    def _update_posts_display_incremental(self, new_posts, previous_total):
        """تحديث عرض المشاركات (جلب تدريجي)"""
        if new_posts:
            # إضافة المشاركات الجديدة في المقدمة
            for post in reversed(new_posts):  # عكس الترتيب لإضافة الأحدث أولاً
                self.posts_tree.insert('', 0, values=(
                    post.get('title', ''),
                    post.get('link', ''),
                    post.get('date', ''),
                    post.get('category', '')
                ))

            self._log_message(f"✅ تم جلب {len(new_posts)} مشاركة جديدة فقط", "SUCCESS")
            self._log_message(f"📊 إجمالي المشاركات المحفوظة: {previous_total + len(new_posts)}", "INFO")
            self._log_message("🎯 تم التوقف عند الوصول للمشاركات القديمة", "INFO")
        else:
            self._log_message("ℹ️ لا توجد مشاركات جديدة منذ آخر مزامنة", "INFO")
            self._log_message(f"📊 إجمالي المشاركات المحفوظة: {previous_total}", "INFO")
            self._log_message("✅ الموقع محدث بالكامل", "SUCCESS")

    def _clear_posts(self):
        """مسح جميع المشاركات المحفوظة"""
        result = messagebox.askyesno("تأكيد المسح", "هل أنت متأكد من مسح جميع المشاركات المحفوظة؟")
        if result:
            try:
                from posts_manager import PostsManager
                posts_manager = PostsManager()
                posts_manager.clear_all_posts()

                # مسح العرض
                for item in self.posts_tree.get_children():
                    self.posts_tree.delete(item)

                self._log_message("✅ تم مسح جميع المشاركات", "SUCCESS")

            except Exception as e:
                self._log_message(f"❌ فشل في مسح المشاركات: {str(e)}", "ERROR")

    def _fetch_categories_for_links(self):
        """جلب التصنيفات من الموقع المحدد في تبويب الروابط"""
        site_id = self.links_site_combo.get()
        if not site_id:
            messagebox.showerror("خطأ", "يرجى اختيار موقع")
            return

        self._log_message(f"📂 بدء جلب التصنيفات من الموقع: {site_id}")

        def fetch_in_background():
            try:
                from posts_manager import PostsManager
                posts_manager = PostsManager()

                # جلب التصنيفات
                categories = posts_manager.fetch_categories(site_id)

                # تحديث الواجهة في الخيط الرئيسي
                self.root.after(0, lambda: self._log_message(f"✅ تم جلب {len(categories)} تصنيف بنجاح", "SUCCESS"))

            except Exception as e:
                error_msg = f"❌ فشل في جلب التصنيفات: {str(e)}"
                self.root.after(0, lambda: self._log_message(error_msg, "ERROR"))

        # تشغيل العملية في خيط منفصل
        thread = threading.Thread(target=fetch_in_background)
        thread.daemon = True
        thread.start()

    def _show_sync_stats(self):
        """عرض إحصائيات المزامنة"""
        site_id = self.links_site_combo.get()
        if not site_id:
            messagebox.showerror("خطأ", "يرجى اختيار موقع")
            return

        try:
            from posts_manager import PostsManager
            posts_manager = PostsManager()
            stats = posts_manager.get_sync_stats(site_id)

            self._log_message(f"📊 إحصائيات المزامنة - {site_id}", "SUCCESS")

            if stats['is_synced']:
                self._log_message(f"📅 آخر مزامنة: {stats['last_sync']}", "INFO")
                self._log_message(f"📝 المشاركات المحفوظة: {stats['total_stored']}", "INFO")
                self._log_message(f"🆔 آخر معرف مشاركة: {stats['last_post_id']}", "INFO")
                self._log_message("✅ الموقع متزامن", "SUCCESS")
            else:
                self._log_message("⚠️ لم يتم تزامن الموقع بعد", "WARNING")
                self._log_message("💡 استخدم 'جلب كامل' لبدء المزامنة", "INFO")

        except Exception as e:
            self._log_message(f"❌ فشل في عرض الإحصائيات: {str(e)}", "ERROR")

    def _create_sites_tab(self):
        """إنشاء تبويب إدارة المواقع"""
        # إطار إضافة موقع جديد
        add_frame = tk.LabelFrame(self.sites_tab, text="إضافة موقع جديد", font=('Arial', 10, 'bold'))
        add_frame.pack(fill='x', pady=5, padx=5)

        # حقول إدخال بيانات الموقع
        tk.Label(add_frame, text="اسم الموقع:", font=('Arial', 9)).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        site_name_entry = tk.Entry(add_frame, textvariable=self.site_name_var, font=('Arial', 9), width=30)
        site_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky='ew')
        self._create_context_menu(site_name_entry)

        tk.Label(add_frame, text="اسم المستخدم:", font=('Arial', 9)).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        username_entry = tk.Entry(add_frame, textvariable=self.username_var, font=('Arial', 9), width=30)
        username_entry.grid(row=1, column=1, padx=5, pady=5, sticky='ew')
        self._create_context_menu(username_entry)

        tk.Label(add_frame, text="كلمة مرور التطبيق:", font=('Arial', 9)).grid(row=2, column=0, sticky='w', padx=5, pady=5)
        password_entry = tk.Entry(add_frame, textvariable=self.password_var, font=('Arial', 9), width=30, show="*")
        password_entry.grid(row=2, column=1, padx=5, pady=5, sticky='ew')
        self._create_context_menu(password_entry)

        tk.Label(add_frame, text="رابط الموقع:", font=('Arial', 9)).grid(row=3, column=0, sticky='w', padx=5, pady=5)
        url_entry = tk.Entry(add_frame, textvariable=self.url_var, font=('Arial', 9), width=30)
        url_entry.grid(row=3, column=1, padx=5, pady=5, sticky='ew')
        self._create_context_menu(url_entry)

        # أزرار إدارة المواقع
        buttons_frame = tk.Frame(add_frame)
        buttons_frame.grid(row=4, column=0, columnspan=2, pady=10)

        tk.Button(
            buttons_frame,
            text="➕ إضافة موقع",
            command=self._add_site,
            bg='#27ae60',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="🔄 تحديث موقع",
            command=self._update_site,
            bg='#3498db',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="🗑️ حذف موقع",
            command=self._delete_site,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        add_frame.columnconfigure(1, weight=1)

        # إطار قائمة المواقع
        list_frame = tk.LabelFrame(self.sites_tab, text="المواقع المحفوظة", font=('Arial', 10, 'bold'))
        list_frame.pack(fill='both', expand=True, pady=5, padx=5)

        # قائمة المواقع
        self.sites_listbox = tk.Listbox(list_frame, font=('Arial', 9), height=8)
        self.sites_listbox.pack(fill='both', expand=True, padx=5, pady=5)
        self.sites_listbox.bind('<<ListboxSelect>>', self._on_site_select)
        self._create_context_menu(self.sites_listbox)

        # تحديث قائمة المواقع
        self._refresh_sites_list()

    def _create_links_tab(self):
        """إنشاء تبويب الروابط الداخلية"""
        # إطار جلب المشاركات
        fetch_frame = tk.LabelFrame(self.links_tab, text="جلب المشاركات من الموقع", font=('Arial', 10, 'bold'))
        fetch_frame.pack(fill='x', pady=5, padx=5)

        # اختيار الموقع
        tk.Label(fetch_frame, text="اختر الموقع:", font=('Arial', 9)).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.links_site_combo = ttk.Combobox(fetch_frame, state="readonly", width=30)
        self.links_site_combo.grid(row=0, column=1, padx=5, pady=5, sticky='w')

        # أزرار العمليات
        buttons_frame = tk.Frame(fetch_frame)
        buttons_frame.grid(row=1, column=0, columnspan=2, pady=10)

        tk.Button(
            buttons_frame,
            text="🔄 جلب جديد",
            command=self._fetch_posts_incremental,
            bg='#3498db',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="🔄 جلب كامل",
            command=self._fetch_posts_full,
            bg='#2980b9',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="🗑️ مسح البيانات",
            command=self._clear_posts,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="📂 جلب التصنيفات",
            command=self._fetch_categories_for_links,
            bg='#9b59b6',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        tk.Button(
            buttons_frame,
            text="📊 إحصائيات",
            command=self._show_sync_stats,
            bg='#34495e',
            fg='white',
            font=('Arial', 9, 'bold')
        ).pack(side='left', padx=5)

        # إطار عرض المشاركات
        posts_frame = tk.LabelFrame(self.links_tab, text="المشاركات المحفوظة", font=('Arial', 10, 'bold'))
        posts_frame.pack(fill='both', expand=True, pady=5, padx=5)

        # جدول المشاركات
        columns = ('العنوان', 'الرابط', 'التاريخ', 'الفئة')
        self.posts_tree = ttk.Treeview(posts_frame, columns=columns, show='headings', height=15)

        # تحديد عناوين الأعمدة
        self.posts_tree.heading('العنوان', text='العنوان')
        self.posts_tree.heading('الرابط', text='الرابط')
        self.posts_tree.heading('التاريخ', text='التاريخ')
        self.posts_tree.heading('الفئة', text='الفئة')

        # تحديد عرض الأعمدة
        self.posts_tree.column('العنوان', width=300)
        self.posts_tree.column('الرابط', width=200)
        self.posts_tree.column('التاريخ', width=100)
        self.posts_tree.column('الفئة', width=100)

        # شريط التمرير
        posts_scrollbar = ttk.Scrollbar(posts_frame, orient='vertical', command=self.posts_tree.yview)
        self.posts_tree.configure(yscrollcommand=posts_scrollbar.set)

        # تخطيط الجدول وشريط التمرير
        self.posts_tree.pack(side='left', fill='both', expand=True)
        posts_scrollbar.pack(side='right', fill='y')

        # إضافة القائمة السياقية للجدول
        self._create_context_menu(self.posts_tree)

        # تحديث قائمة المواقع في التبويب (بعد إنشاء العناصر)
        self._update_links_sites_list()

    def _log_message(self, message: str, level: str = "INFO"):
        """إضافة رسالة إلى السجل"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # تحديد لون الرسالة حسب المستوى
        colors = {
            "INFO": "#3498db",
            "SUCCESS": "#27ae60", 
            "WARNING": "#f39c12",
            "ERROR": "#e74c3c"
        }
        
        color = colors.get(level, "#ecf0f1")
        
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
        # تحديث شريط الحالة
        if level == "ERROR":
            self.status_var.set(f"❌ خطأ: {message[:50]}...")
        elif level == "SUCCESS":
            self.status_var.set(f"✅ {message[:50]}...")
        else:
            self.status_var.set(f"ℹ️ {message[:50]}...")
        
        self.root.update()
    
    def _validate_keyword(self) -> bool:
        """التحقق من صحة الكلمة المفتاحية والموقع والتصنيف"""
        keyword = self.keyword_var.get().strip()
        if not keyword:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة مفتاحية")
            return False

        site = self.site_var.get().strip()
        if not site:
            messagebox.showerror("خطأ", "يرجى اختيار موقع للنشر")
            return False

        category = self.category_var.get().strip()
        if not category:
            result = messagebox.askyesno(
                "تأكيد",
                "لم يتم اختيار تصنيف. هل تريد المتابعة بدون تصنيف محدد؟"
            )
            if not result:
                return False

        return True

    def _get_selected_category_id(self) -> Optional[int]:
        """استخراج معرف التصنيف المحدد"""
        category_text = self.category_var.get().strip()
        if not category_text:
            return None

        # استخراج المعرف من النص: "اسم التصنيف (ID: 123)"
        import re
        match = re.search(r'\(ID: (\d+)\)', category_text)
        if match:
            return int(match.group(1))

        return None
    
    def _run_in_thread(self, func, *args):
        """تشغيل دالة في thread منفصل"""
        thread = threading.Thread(target=func, args=args, daemon=True)
        thread.start()
    
    def _create_main_article(self):
        """إنشاء مقال رئيسي"""
        if not self._validate_keyword():
            return
        
        keyword = self.keyword_var.get().strip()
        self._log_message(f"📝 بدء إنشاء المقال الرئيسي للكلمة: {keyword}")
        self._run_in_thread(self._create_main_article_worker, keyword)
    
    def _create_main_article_worker(self, keyword: str):
        """العامل الفعلي لإنشاء المقال الرئيسي"""
        try:
            self.status_var.set("⏳ جاري إنشاء المقال الرئيسي...")

            site_id = self.site_var.get().strip() if self.site_var.get().strip() else None
            category_id = self._get_selected_category_id()
            filepath, related_keywords = self.content_manager.generate_main_article(keyword, site_id, category_id)
            
            self._log_message(f"✅ تم إنشاء المقال الرئيسي بنجاح", "SUCCESS")
            self._log_message(f"📁 المسار: {filepath}")
            
            if related_keywords:
                self._log_message(f"🔑 تم استخراج {len(related_keywords)} كلمة مفتاحية فرعية", "SUCCESS")
                for i, kw in enumerate(related_keywords[:5], 1):  # عرض أول 5 فقط
                    self._log_message(f"  {i}. {kw}")
                if len(related_keywords) > 5:
                    self._log_message(f"  ... و {len(related_keywords) - 5} كلمة أخرى")
            
            self.status_var.set("✅ تم إنشاء المقال الرئيسي بنجاح")
            
        except Exception as e:
            self._log_message(f"❌ فشل في إنشاء المقال: {str(e)}", "ERROR")
            logger.error(f"خطأ في إنشاء المقال الرئيسي: {keyword}", e)
    
    def _create_sub_articles(self):
        """إنشاء مقالات فرعية"""
        if not self._validate_keyword():
            return
        
        keyword = self.keyword_var.get().strip()
        self._log_message(f"📚 بدء إنشاء المقالات الفرعية للكلمة: {keyword}")
        self._run_in_thread(self._create_sub_articles_worker, keyword)
    
    def _create_sub_articles_worker(self, keyword: str):
        """العامل الفعلي لإنشاء المقالات الفرعية"""
        try:
            self.status_var.set("⏳ جاري إنشاء المقالات الفرعية...")

            site_id = self.site_var.get().strip() if self.site_var.get().strip() else None
            category_id = self._get_selected_category_id()
            generated_files = self.content_manager.generate_sub_articles(keyword, site_id=site_id, category_id=category_id)
            
            if generated_files:
                self._log_message(f"✅ تم إنشاء {len(generated_files)} مقال فرعي بنجاح", "SUCCESS")
                self.status_var.set(f"✅ تم إنشاء {len(generated_files)} مقال فرعي")
            else:
                self._log_message("⚠️ لم يتم إنشاء أي مقال فرعي", "WARNING")
                self.status_var.set("⚠️ لم يتم إنشاء مقالات فرعية")
            
        except Exception as e:
            self._log_message(f"❌ فشل في إنشاء المقالات الفرعية: {str(e)}", "ERROR")
            logger.error(f"خطأ في إنشاء المقالات الفرعية: {keyword}", e)
    
    def _publish_single(self):
        """نشر مقال واحد"""
        site = self.site_var.get()
        self._log_message(f"🌐 بدء نشر مقال واحد على موقع: {site}")
        self._run_in_thread(self._publish_worker, site, False)
    
    def _publish_all(self):
        """نشر جميع المقالات"""
        result = messagebox.askyesno(
            "تأكيد", 
            "هل أنت متأكد من نشر جميع المقالات؟\nهذه العملية قد تستغرق وقتاً طويلاً."
        )
        
        if result:
            site = self.site_var.get()
            self._log_message(f"🌐 بدء نشر جميع المقالات على موقع: {site}")
            self._run_in_thread(self._publish_worker, site, True)
    
    def _publish_worker(self, site: str, publish_all: bool):
        """العامل الفعلي للنشر"""
        try:
            self.status_var.set("⏳ جاري النشر...")

            # البحث عن ملفات المقالات
            articles_dir = "articles"

            if not os.path.exists(articles_dir):
                self._log_message(f"❌ مجلد المقالات غير موجود: {articles_dir}", "ERROR")
                return

            # البحث عن ملفات المقالات
            article_files = []
            for filename in os.listdir(articles_dir):
                if filename.endswith(".txt"):
                    article_files.append(os.path.join(articles_dir, filename))

            # البحث في المجلدات الفرعية أيضاً
            for item in os.listdir(articles_dir):
                item_path = os.path.join(articles_dir, item)
                if os.path.isdir(item_path):
                    for filename in os.listdir(item_path):
                        if filename.endswith(".txt"):
                            article_files.append(os.path.join(item_path, filename))

            if not article_files:
                self._log_message("⚠️ لا توجد مقالات للنشر", "WARNING")
                return

            published_count = 0

            for article_file in article_files:
                try:
                    # تحليل المقال
                    article = self.publisher.parse_article(article_file)

                    # البحث عن صورة مرتبطة
                    image_path = self.publisher.find_article_image(article_file)

                    # الحصول على معرف التصنيف
                    category_id = self._get_selected_category_id()

                    # نشر المقال
                    success, result = self.publisher.publish_to_wordpress(
                        article, site, image_path, category_id
                    )

                    if success:
                        published_count += 1
                        self._log_message(f"✅ تم نشر: {article.get('title', 'بدون عنوان')}", "SUCCESS")
                        self._log_message(f"   الرابط: {result}")

                        # نقل إلى مجلد المنشورات
                        self.publisher.move_to_published(article_file, image_path)

                        if not publish_all:
                            break  # نشر مقال واحد فقط
                    else:
                        self._log_message(f"❌ فشل نشر المقال: {article_file} - {result}", "ERROR")

                except Exception as e:
                    self._log_message(f"❌ خطأ في معالجة المقال: {article_file}", "ERROR")
                    continue

            if published_count > 0:
                self._log_message(f"✅ تم نشر {published_count} مقال بنجاح", "SUCCESS")
                self.status_var.set(f"✅ تم نشر {published_count} مقال")
            else:
                self._log_message("⚠️ لم يتم نشر أي مقال", "WARNING")
                self.status_var.set("⚠️ فشل في النشر")

        except Exception as e:
            self._log_message(f"❌ فشل في النشر: {str(e)}", "ERROR")
            logger.error("خطأ في عملية النشر", e)
    
    def _show_stats(self):
        """عرض إحصائيات مقال"""
        if not self._validate_keyword():
            return
        
        keyword = self.keyword_var.get().strip()
        self._run_in_thread(self._show_stats_worker, keyword)
    
    def _show_stats_worker(self, keyword: str):
        """العامل الفعلي لعرض الإحصائيات"""
        try:
            stats = db.get_article_stats(keyword)
            
            if not stats:
                self._log_message(f"❌ المقال غير موجود: {keyword}", "ERROR")
                return
            
            self._log_message(f"📊 إحصائيات المقال: {keyword}", "SUCCESS")
            self._log_message(f"📝 عدد الكلمات: {stats['word_count']:,}")
            self._log_message(f"🎯 كثافة الكلمة المفتاحية: {stats['keyword_density']:.2f}%")
            self._log_message(f"📅 تاريخ الإنشاء: {stats['created_at']}")
            
            if stats.get('related_keywords'):
                self._log_message(f"🔑 الكلمات المفتاحية: {len(stats['related_keywords'])}")
            
            if stats.get('publications'):
                self._log_message(f"🌐 المنشورات: {len(stats['publications'])}")
            
        except Exception as e:
            self._log_message(f"❌ فشل في عرض الإحصائيات: {str(e)}", "ERROR")
            logger.error(f"خطأ في عرض إحصائيات المقال: {keyword}", e)
    
    def _show_dashboard(self):
        """عرض لوحة التحكم"""
        self._run_in_thread(self._show_dashboard_worker)
    
    def _show_dashboard_worker(self):
        """العامل الفعلي لعرض لوحة التحكم"""
        try:
            stats = db.get_dashboard_stats()
            
            self._log_message("📊 لوحة التحكم - ContentMaster Pro", "SUCCESS")
            self._log_message(f"📝 المقالات الرئيسية: {stats.get('main_articles', 0)}")
            self._log_message(f"📚 المقالات الفرعية: {stats.get('sub_articles', 0)}")
            self._log_message(f"🌐 المنشورات: {stats.get('publications', 0)}")
            self._log_message(f"📊 إجمالي الكلمات: {stats.get('total_words', 0):,}")
            
            if stats.get('articles_by_status'):
                self._log_message("📈 المقالات حسب الحالة:")
                for status, count in stats['articles_by_status'].items():
                    self._log_message(f"  • {status}: {count}")
            
        except Exception as e:
            self._log_message(f"❌ فشل في عرض لوحة التحكم: {str(e)}", "ERROR")
            logger.error("خطأ في عرض لوحة التحكم", e)
    
    def run(self):
        """تشغيل الواجهة الرسومية"""
        try:
            self.root.mainloop()
        except Exception as e:
            logger.error("خطأ في تشغيل الواجهة الرسومية", e)

def main():
    """الدالة الرئيسية للواجهة الرسومية"""
    try:
        app = ContentMasterGUI()
        app.run()
    except Exception as e:
        logger.error("فشل في تشغيل الواجهة الرسومية", e)
        print(f"❌ خطأ في تشغيل الواجهة الرسومية: {str(e)}")

if __name__ == "__main__":
    main()
