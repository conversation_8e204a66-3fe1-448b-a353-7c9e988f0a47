"""إعدادات البرنامج الرئيسية - محسن ومنظم مع نظام إدارة مفاتيح API متقدم"""
import os
import json
import random
import time
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class APIKey:
    """فئة لتمثيل مفتاح API"""
    key: str
    provider: str  # 'gemini' أو 'openrouter'
    name: str
    is_active: bool = True
    usage_count: int = 0
    last_used: Optional[str] = None
    rate_limit_reset: Optional[str] = None
    daily_limit: Optional[int] = None
    monthly_limit: Optional[int] = None

@dataclass
class GeminiModel:
    """فئة لتمثيل نموذج Gemini"""
    name: str
    model_id: str
    description: str
    max_tokens: int
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 40
    is_free: bool = True
    provider: str = "gemini"

@dataclass
class OpenRouterModel:
    """فئة لتمثيل نموذج OpenRouter"""
    name: str
    model_id: str
    description: str
    max_tokens: int
    temperature: float = 0.7
    is_free: bool = True
    provider: str = "openrouter"
    context_length: int = 4096

class Config:
    """إدارة إعدادات البرنامج المحسنة مع نظام إدارة مفاتيح API متقدم"""

    # 🔑 مفاتيح API الافتراضية (للتوافق مع النسخة القديمة)
    GEMINI_API_KEY = "AIzaSyDiQUr0lCE_D-nbcsxM9YjpwzRUjH-tJdA"

    # 🔑 مفاتيح API المتعددة الافتراضية
    DEFAULT_API_KEYS = [
        APIKey(
            key="AIzaSyDiQUr0lCE_D-nbcsxM9YjpwzRUjH-tJdA",
            provider="gemini",
            name="Gemini Key 1",
            is_active=True
        )
    ]

    # 🤖 نماذج Gemini المتاحة (محدثة وموسعة مع جميع النماذج المجانية)
    GEMINI_MODELS = {
        "gemini-2.0-flash-exp": GeminiModel(
            name="Gemini 2.0 Flash (تجريبي)",
            model_id="gemini-2.0-flash-exp",
            description="أحدث نموذج تجريبي - سريع ومتطور مع قدرات محسنة",
            max_tokens=8192,
            temperature=0.7,
            is_free=True
        ),
        "gemini-1.5-pro": GeminiModel(
            name="Gemini 1.5 Pro",
            model_id="gemini-1.5-pro",
            description="نموذج متقدم للمهام المعقدة - أفضل للتحليل العميق",
            max_tokens=8192,
            temperature=0.7,
            is_free=True
        ),
        "gemini-1.5-flash": GeminiModel(
            name="Gemini 1.5 Flash",
            model_id="gemini-1.5-flash",
            description="نموذج سريع ومتوازن - مثالي للمهام العامة",
            max_tokens=8192,
            temperature=0.7,
            is_free=True
        ),
        "gemini-1.5-flash-8b": GeminiModel(
            name="Gemini 1.5 Flash 8B",
            model_id="gemini-1.5-flash-8b",
            description="نموذج خفيف وسريع جداً - مثالي للمهام البسيطة",
            max_tokens=8192,
            temperature=0.7,
            is_free=True
        ),
        "gemini-pro": GeminiModel(
            name="Gemini Pro",
            model_id="gemini-pro",
            description="النموذج الكلاسيكي المتوازن - موثوق للمهام المتنوعة",
            max_tokens=4096,
            temperature=0.7,
            is_free=True
        ),
        "gemini-pro-vision": GeminiModel(
            name="Gemini Pro Vision",
            model_id="gemini-pro-vision",
            description="نموذج متخصص في فهم الصور والنصوص معاً",
            max_tokens=4096,
            temperature=0.7,
            is_free=True
        )
    }

    # 🌐 نماذج OpenRouter (سيتم تحديثها ديناميكياً)
    OPENROUTER_MODELS = {}

    # 🎨 نماذج إنتاج الصور
    IMAGE_GENERATION_MODELS = {
        "black-forest-labs/flux-1-schnell:free": {
            "name": "FLUX.1 Schnell (مجاني)",
            "provider": "openrouter",
            "description": "نموذج سريع لإنتاج صور عالية الجودة",
            "max_resolution": "1024x1024",
            "is_free": True
        },
        "stabilityai/stable-diffusion-3-medium:free": {
            "name": "Stable Diffusion 3 Medium (مجاني)",
            "provider": "openrouter",
            "description": "نموذج متقدم لإنتاج صور واقعية",
            "max_resolution": "1024x1024",
            "is_free": True
        }
    }
    
    # 📁 ملفات التكوين
    SITES_CONFIG_FILE = "sites_config.json"
    USER_SETTINGS_FILE = "user_settings.json"
    API_KEYS_CONFIG_FILE = "api_keys_config.json"
    
    # 📁 مجلدات النظام
    DIRECTORIES = {
        "articles": "articles",
        "keywords": "keywords", 
        "published": "published",
        "database": "database",
        "prompts": "prompts",
        "logs": "logs",
        "temp": "temp"
    }
    
    # 🤖 إعدادات الذكاء الاصطناعي الافتراضية
    DEFAULT_AI_SETTINGS = {
        "model": "gemini-1.5-flash",  # النموذج الافتراضي
        "max_retries": 3,
        "retry_delay": 5,
        "rate_limit_delay": (6, 15),
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "model_settings": {
            "gemini-2.0-flash-exp": {
                "max_retries": 5,
                "retry_delay": 10,
                "rate_limit_delay": (15, 30)
            },
            "gemini-1.5-pro": {
                "max_retries": 5,
                "retry_delay": 15,
                "rate_limit_delay": (30, 60)
            },
            "gemini-1.5-flash": {
                "max_retries": 4,
                "retry_delay": 8,
                "rate_limit_delay": (10, 20)
            }
        }
    }
    
    # 📝 إعدادات المحتوى
    CONTENT_SETTINGS = {
        "main_article_min_words": 2500,
        "sub_article_min_words": 2000,
        "keyword_density": 1.3,
        "related_keyword_density": (0.4, 0.8),
        "meta_description_max_chars": 160
    }
    
    # 📊 إعدادات قاعدة البيانات
    DATABASE_SETTINGS = {
        "name": "content_manager.db"
    }
    
    # 🌐 إعدادات WordPress الافتراضية
    DEFAULT_WORDPRESS_SITES = {
        "mshru3": {
            "name": "مشروع",
            "username": "m.m3rfa",
            "app_password": "j3mG Ldf9 izsJ kY3J Iri2 snMb",
            "site_url": "https://www.mshru3.com",
            "categories": {
                "تقنية": 94,
                "الربح من الانترنت": 75,
                "سفر وسياحة": 127,
                "صحة": 95,
                "مال و استثمار": 85
            }
        }
    }
    
    # إعدادات المستخدم المحملة
    _user_settings = {}
    _wordpress_sites = {}
    _api_keys = []
    _current_api_key_index = 0
    
    @classmethod
    def initialize(cls) -> None:
        """تهيئة النظام وتحميل الإعدادات"""
        cls.ensure_directories()
        cls._load_user_settings()
        cls._load_sites_config()
        cls._load_api_keys_config()
        cls._load_openrouter_models()
    
    @classmethod
    def ensure_directories(cls) -> None:
        """إنشاء المجلدات المطلوبة"""
        for dir_name, dir_path in cls.DIRECTORIES.items():
            os.makedirs(dir_path, exist_ok=True)
    
    @classmethod
    def _load_user_settings(cls) -> None:
        """تحميل إعدادات المستخدم"""
        try:
            if os.path.exists(cls.USER_SETTINGS_FILE):
                with open(cls.USER_SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    cls._user_settings = json.load(f)
            else:
                cls._user_settings = cls.DEFAULT_AI_SETTINGS.copy()
                cls._save_user_settings()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات المستخدم: {e}")
            cls._user_settings = cls.DEFAULT_AI_SETTINGS.copy()
    
    @classmethod
    def _save_user_settings(cls) -> None:
        """حفظ إعدادات المستخدم"""
        try:
            with open(cls.USER_SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(cls._user_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات المستخدم: {e}")
    
    @classmethod
    def _load_sites_config(cls) -> None:
        """تحميل إعدادات المواقع"""
        try:
            if os.path.exists(cls.SITES_CONFIG_FILE):
                with open(cls.SITES_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    cls._wordpress_sites = json.load(f)
            else:
                cls._wordpress_sites = cls.DEFAULT_WORDPRESS_SITES.copy()
                cls._save_sites_config()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات المواقع: {e}")
            cls._wordpress_sites = cls.DEFAULT_WORDPRESS_SITES.copy()
    
    @classmethod
    def _save_sites_config(cls) -> None:
        """حفظ إعدادات المواقع"""
        try:
            with open(cls.SITES_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(cls._wordpress_sites, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات المواقع: {e}")

    @classmethod
    def _load_api_keys_config(cls) -> None:
        """تحميل إعدادات مفاتيح API"""
        try:
            if os.path.exists(cls.API_KEYS_CONFIG_FILE):
                with open(cls.API_KEYS_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    keys_data = json.load(f)
                    cls._api_keys = []
                    for key_data in keys_data.get('api_keys', []):
                        api_key = APIKey(
                            key=key_data['key'],
                            provider=key_data['provider'],
                            name=key_data['name'],
                            is_active=key_data.get('is_active', True),
                            usage_count=key_data.get('usage_count', 0),
                            last_used=key_data.get('last_used'),
                            rate_limit_reset=key_data.get('rate_limit_reset'),
                            daily_limit=key_data.get('daily_limit'),
                            monthly_limit=key_data.get('monthly_limit')
                        )
                        cls._api_keys.append(api_key)
                    cls._current_api_key_index = keys_data.get('current_index', 0)
            else:
                cls._api_keys = cls.DEFAULT_API_KEYS.copy()
                cls._current_api_key_index = 0
                cls._save_api_keys_config()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات مفاتيح API: {e}")
            cls._api_keys = cls.DEFAULT_API_KEYS.copy()
            cls._current_api_key_index = 0

    @classmethod
    def _save_api_keys_config(cls) -> None:
        """حفظ إعدادات مفاتيح API"""
        try:
            keys_data = {
                'api_keys': [],
                'current_index': cls._current_api_key_index
            }

            for api_key in cls._api_keys:
                key_data = {
                    'key': api_key.key,
                    'provider': api_key.provider,
                    'name': api_key.name,
                    'is_active': api_key.is_active,
                    'usage_count': api_key.usage_count,
                    'last_used': api_key.last_used,
                    'rate_limit_reset': api_key.rate_limit_reset,
                    'daily_limit': api_key.daily_limit,
                    'monthly_limit': api_key.monthly_limit
                }
                keys_data['api_keys'].append(key_data)

            with open(cls.API_KEYS_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(keys_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات مفاتيح API: {e}")

    # === إدارة مفاتيح API ===

    @classmethod
    def get_api_keys(cls) -> List[APIKey]:
        """الحصول على جميع مفاتيح API"""
        return cls._api_keys

    @classmethod
    def get_active_api_keys(cls, provider: str = None) -> List[APIKey]:
        """الحصول على مفاتيح API النشطة"""
        active_keys = [key for key in cls._api_keys if key.is_active]
        if provider:
            active_keys = [key for key in active_keys if key.provider == provider]
        return active_keys

    @classmethod
    def get_current_api_key(cls, provider: str = "gemini") -> Optional[APIKey]:
        """الحصول على مفتاح API الحالي"""
        active_keys = cls.get_active_api_keys(provider)
        if not active_keys:
            return None

        # التأكد من أن الفهرس صحيح
        if cls._current_api_key_index >= len(active_keys):
            cls._current_api_key_index = 0

        return active_keys[cls._current_api_key_index]

    @classmethod
    def rotate_api_key(cls, provider: str = "gemini") -> Optional[APIKey]:
        """تدوير مفتاح API إلى التالي"""
        active_keys = cls.get_active_api_keys(provider)
        if len(active_keys) <= 1:
            return cls.get_current_api_key(provider)

        cls._current_api_key_index = (cls._current_api_key_index + 1) % len(active_keys)
        cls._save_api_keys_config()
        return active_keys[cls._current_api_key_index]

    @classmethod
    def add_api_key(cls, key: str, provider: str, name: str, **kwargs) -> bool:
        """إضافة مفتاح API جديد"""
        try:
            api_key = APIKey(
                key=key,
                provider=provider,
                name=name,
                is_active=kwargs.get('is_active', True),
                daily_limit=kwargs.get('daily_limit'),
                monthly_limit=kwargs.get('monthly_limit')
            )
            cls._api_keys.append(api_key)
            cls._save_api_keys_config()
            return True
        except Exception as e:
            print(f"خطأ في إضافة مفتاح API: {e}")
            return False

    @classmethod
    def remove_api_key(cls, key: str) -> bool:
        """حذف مفتاح API"""
        try:
            cls._api_keys = [k for k in cls._api_keys if k.key != key]
            cls._save_api_keys_config()
            return True
        except Exception as e:
            print(f"خطأ في حذف مفتاح API: {e}")
            return False

    @classmethod
    def update_api_key_usage(cls, key: str) -> None:
        """تحديث إحصائيات استخدام مفتاح API"""
        try:
            for api_key in cls._api_keys:
                if api_key.key == key:
                    api_key.usage_count += 1
                    api_key.last_used = datetime.now().isoformat()
                    break
            cls._save_api_keys_config()
        except Exception as e:
            print(f"خطأ في تحديث إحصائيات مفتاح API: {e}")

    @classmethod
    def set_api_key_rate_limit(cls, key: str, reset_time: str) -> None:
        """تعيين وقت إعادة تعيين حد الاستخدام لمفتاح API"""
        try:
            for api_key in cls._api_keys:
                if api_key.key == key:
                    api_key.rate_limit_reset = reset_time
                    break
            cls._save_api_keys_config()
        except Exception as e:
            print(f"خطأ في تعيين حد الاستخدام لمفتاح API: {e}")

    @classmethod
    def _load_openrouter_models(cls) -> None:
        """تحميل نماذج OpenRouter الحقيقية"""
        try:
            # استيراد عميل OpenRouter
            from openrouter_client import openrouter_client

            # جلب النماذج المتاحة
            models_data = openrouter_client.get_available_models()

            if models_data:
                cls.OPENROUTER_MODELS = {}
                free_models_count = 0

                # قائمة النماذج المجانية المجربة والتي تعمل بشكل صحيح
                working_free_models = [
                    "meta-llama/llama-3.1-8b-instruct:free",
                    "microsoft/phi-3-medium-128k-instruct:free",
                    "microsoft/phi-3-mini-128k-instruct:free",
                    "google/gemma-2-9b-it:free",
                    "qwen/qwen-2-7b-instruct:free",
                    "huggingfaceh4/zephyr-7b-beta:free",
                    "openchat/openchat-7b:free",
                    "mistralai/mistral-7b-instruct:free"
                ]

                for model_data in models_data:
                    model_id = model_data.get("id", "")

                    # التحقق من أن النموذج في قائمة النماذج المجربة
                    if model_id in working_free_models:
                        # التحقق من أن النموذج مجاني
                        pricing = model_data.get("pricing", {})
                        prompt_price = float(pricing.get("prompt", "1"))
                        completion_price = float(pricing.get("completion", "1"))

                        if prompt_price == 0 and completion_price == 0:
                            # إنشاء نموذج OpenRouter
                            openrouter_model = OpenRouterModel(
                                name=model_data.get("name", model_id),
                                model_id=model_id,
                                description=model_data.get("description", "نموذج مجاني من OpenRouter"),
                                max_tokens=model_data.get("top_provider", {}).get("max_completion_tokens", 4096),
                                context_length=model_data.get("context_length", 4096),
                                is_free=True
                            )

                            cls.OPENROUTER_MODELS[model_id] = openrouter_model
                            free_models_count += 1

                print(f"✅ تم تحميل {free_models_count} نموذج مجاني من OpenRouter")
            else:
                print("⚠️ لم يتم العثور على نماذج OpenRouter")

        except Exception as e:
            print(f"خطأ في تحميل نماذج OpenRouter: {e}")
            # إضافة نماذج افتراضية في حالة الفشل
            cls.OPENROUTER_MODELS = {
                "nousresearch/nous-capybara-7b:free": OpenRouterModel(
                    name="Nous Capybara 7B (مجاني)",
                    model_id="nousresearch/nous-capybara-7b:free",
                    description="نموذج محادثة متقدم ومجاني",
                    max_tokens=4096,
                    context_length=4096,
                    is_free=True
                )
            }

    # === إدارة نماذج Gemini ===
    
    @classmethod
    def get_available_models(cls) -> Dict[str, Union[GeminiModel, OpenRouterModel]]:
        """الحصول على جميع النماذج المتاحة (Gemini + OpenRouter)"""
        all_models = {}
        all_models.update(cls.GEMINI_MODELS)
        all_models.update(cls.OPENROUTER_MODELS)
        return all_models

    @classmethod
    def get_gemini_models(cls) -> Dict[str, GeminiModel]:
        """الحصول على نماذج Gemini فقط"""
        return cls.GEMINI_MODELS

    @classmethod
    def get_openrouter_models(cls) -> Dict[str, OpenRouterModel]:
        """الحصول على نماذج OpenRouter فقط"""
        return cls.OPENROUTER_MODELS

    @classmethod
    def get_image_generation_models(cls) -> Dict[str, Dict]:
        """الحصول على نماذج إنتاج الصور"""
        return cls.IMAGE_GENERATION_MODELS

    @classmethod
    def get_free_models(cls) -> Dict[str, Union[GeminiModel, OpenRouterModel]]:
        """الحصول على النماذج المجانية فقط"""
        free_models = {}

        # إضافة نماذج Gemini المجانية
        for model_id, model in cls.GEMINI_MODELS.items():
            if model.is_free:
                free_models[model_id] = model

        # إضافة نماذج OpenRouter المجانية
        for model_id, model in cls.OPENROUTER_MODELS.items():
            if model.is_free:
                free_models[model_id] = model

        return free_models
    
    @classmethod
    def get_current_model(cls) -> Union[GeminiModel, OpenRouterModel]:
        """الحصول على النموذج الحالي"""
        model_id = cls._user_settings.get("model", cls.DEFAULT_AI_SETTINGS["model"])

        # البحث في نماذج Gemini أولاً
        if model_id in cls.GEMINI_MODELS:
            return cls.GEMINI_MODELS[model_id]

        # البحث في نماذج OpenRouter
        if model_id in cls.OPENROUTER_MODELS:
            return cls.OPENROUTER_MODELS[model_id]

        # العودة إلى النموذج الافتراضي
        return cls.GEMINI_MODELS["gemini-1.5-flash"]
    
    @classmethod
    def set_current_model(cls, model_id: str) -> bool:
        """تعيين النموذج الحالي"""
        all_models = cls.get_available_models()
        if model_id in all_models:
            cls._user_settings["model"] = model_id
            cls._save_user_settings()
            return True
        return False
    
    @classmethod
    def get_model_settings(cls) -> Dict:
        """الحصول على إعدادات النموذج الحالي"""
        current_model = cls.get_current_model()
        model_id = current_model.model_id
        settings = cls._user_settings.copy()
        
        # استخدام إعدادات النموذج المحدد إذا كانت موجودة
        model_specific_settings = settings.get("model_settings", {}).get(model_id, {})
        
        # دمج الإعدادات العامة مع إعدادات النموذج المحدد
        result = {
            "model_id": model_id,
            "name": current_model.name,
            "description": current_model.description,
            "max_tokens": current_model.max_tokens,
            "temperature": settings.get("temperature", current_model.temperature),
            "max_retries": model_specific_settings.get("max_retries", settings.get("max_retries", 3)),
            "retry_delay": model_specific_settings.get("retry_delay", settings.get("retry_delay", 5)),
            "rate_limit_delay": model_specific_settings.get("rate_limit_delay", settings.get("rate_limit_delay", (6, 15))),
            "provider": current_model.provider,
            "is_free": current_model.is_free
        }

        # إضافة إعدادات خاصة بنماذج Gemini
        if isinstance(current_model, GeminiModel):
            result.update({
                "top_p": settings.get("top_p", current_model.top_p),
                "top_k": settings.get("top_k", current_model.top_k)
            })

        # إضافة إعدادات خاصة بنماذج OpenRouter
        if isinstance(current_model, OpenRouterModel):
            result.update({
                "context_length": current_model.context_length
            })
        
        return result
    
    @classmethod
    def get_model_specific_settings(cls, model_id: str) -> Dict:
        """الحصول على إعدادات نموذج محدد"""
        all_models = cls.get_available_models()
        if model_id not in all_models:
            return {}

        model = all_models[model_id]
        settings = cls._user_settings.copy()
        model_specific_settings = settings.get("model_settings", {}).get(model_id, {})

        result = {
            "model_id": model_id,
            "name": model.name,
            "description": model.description,
            "max_tokens": model.max_tokens,
            "provider": model.provider,
            "is_free": model.is_free,
            "temperature": model_specific_settings.get("temperature", model.temperature),
            "max_retries": model_specific_settings.get("max_retries", settings.get("max_retries", 3)),
            "retry_delay": model_specific_settings.get("retry_delay", settings.get("retry_delay", 5)),
            "rate_limit_delay": model_specific_settings.get("rate_limit_delay", settings.get("rate_limit_delay", (6, 15)))
        }

        # إضافة إعدادات خاصة بنماذج Gemini
        if isinstance(model, GeminiModel):
            result.update({
                "top_p": model_specific_settings.get("top_p", model.top_p),
                "top_k": model_specific_settings.get("top_k", model.top_k)
            })

        # إضافة إعدادات خاصة بنماذج OpenRouter
        if isinstance(model, OpenRouterModel):
            result.update({
                "context_length": model.context_length
            })

        return result
    
    @classmethod
    def update_model_settings(cls, **kwargs) -> None:
        """تحديث إعدادات النموذج"""
        for key, value in kwargs.items():
            if key in ["temperature", "top_p", "top_k", "max_retries", "retry_delay"]:
                cls._user_settings[key] = value
                
        # تحديث إعدادات نموذج محدد إذا تم تحديد model_id
        if "model_id" in kwargs and "settings" in kwargs:
            model_id = kwargs["model_id"]
            model_settings = kwargs["settings"]
            
            if "model_settings" not in cls._user_settings:
                cls._user_settings["model_settings"] = {}
                
            if model_id not in cls._user_settings["model_settings"]:
                cls._user_settings["model_settings"][model_id] = {}
                
            for key, value in model_settings.items():
                if key in ["temperature", "top_p", "top_k", "max_retries", "retry_delay", "rate_limit_delay"]:
                    cls._user_settings["model_settings"][model_id][key] = value
                    
        cls._save_user_settings()
    
    # === إدارة مواقع WordPress ===
    
    @classmethod
    def get_wordpress_sites(cls) -> Dict:
        """الحصول على جميع مواقع WordPress"""
        return cls._wordpress_sites
    
    @classmethod
    def get_wordpress_site(cls, site_id: str) -> Optional[Dict]:
        """الحصول على موقع WordPress محدد"""
        return cls._wordpress_sites.get(site_id)
    
    @classmethod
    def add_wordpress_site(cls, site_id: str, name: str, username: str, 
                          app_password: str, site_url: str, categories: Dict = None) -> None:
        """إضافة موقع WordPress جديد"""
        cls._wordpress_sites[site_id] = {
            "name": name,
            "username": username,
            "app_password": app_password,
            "site_url": site_url,
            "categories": categories or {}
        }
        cls._save_sites_config()
    
    @classmethod
    def update_wordpress_site(cls, site_id: str, **kwargs) -> bool:
        """تحديث موقع WordPress"""
        if site_id in cls._wordpress_sites:
            cls._wordpress_sites[site_id].update(kwargs)
            cls._save_sites_config()
            return True
        return False
    
    @classmethod
    def remove_wordpress_site(cls, site_id: str) -> bool:
        """حذف موقع WordPress"""
        if site_id in cls._wordpress_sites:
            del cls._wordpress_sites[site_id]
            cls._save_sites_config()
            return True
        return False
    
    @classmethod
    def get_site_ids(cls) -> List[str]:
        """الحصول على معرفات المواقع"""
        return list(cls._wordpress_sites.keys())

    # === دوال مساعدة إضافية ===

    @classmethod
    def get_all_sites(cls) -> Dict:
        """الحصول على جميع المواقع (للتوافق مع النسخة القديمة)"""
        return cls._wordpress_sites

    @classmethod
    def get_model_by_task_type(cls, task_type: str) -> str:
        """اختيار أفضل نموذج لنوع مهمة محدد (تم إلغاء التدوير التلقائي)"""
        # إرجاع النموذج الحالي المختار من قبل المستخدم
        current_model = cls.get_current_model()
        return current_model.model_id

    @classmethod
    def get_smart_api_key(cls, provider: str = "gemini", force_rotate: bool = False) -> Optional[str]:
        """الحصول على مفتاح API بذكاء مع التدوير التلقائي"""
        if force_rotate:
            api_key = cls.rotate_api_key(provider)
        else:
            api_key = cls.get_current_api_key(provider)

        if api_key and api_key.is_active:
            # تحديث إحصائيات الاستخدام
            cls.update_api_key_usage(api_key.key)
            return api_key.key

        return None

    @classmethod
    def is_model_available(cls, model_id: str) -> bool:
        """التحقق من توفر نموذج معين"""
        return model_id in cls.get_available_models()

    @classmethod
    def get_model_provider(cls, model_id: str) -> Optional[str]:
        """الحصول على مزود الخدمة لنموذج معين"""
        all_models = cls.get_available_models()
        if model_id in all_models:
            return all_models[model_id].provider
        return None

# تهيئة النظام عند الاستيراد
Config.initialize()
