"""
مدير النشر على WordPress ومنصات أخرى
"""
import os
import shutil
import requests
from requests.auth import HTTPBasicAuth
import mimetypes
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from config import Config
from logger import logger
from database import db

class Publisher:
    """مدير النشر على المنصات المختلفة"""
    
    def __init__(self):
        """تهيئة مدير النشر"""
        Config.ensure_directories()
        logger.info("تم تهيئة مدير النشر بنجاح")
    
    def parse_article(self, file_path: str) -> Dict:
        """تحليل ملف المقال واستخراج البيانات الوصفية والمحتوى"""
        metadata = {
            "title": "",
            "seo_title": "",
            "meta_description": "",
            "category": "",
            "tags": [],
            "status": "draft",
            "content": ""
        }
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            body_lines = []
            for line in lines:
                if line.startswith("#"):
                    parts = line[1:].split(":", 1)
                    if len(parts) == 2:
                        key = parts[0].strip().lower()
                        value = parts[1].strip()
                        if key == "tags":
                            metadata["tags"] = [t.strip() for t in value.split(",")]
                        else:
                            metadata[key] = value
                else:
                    body_lines.append(line)
            
            metadata["content"] = "".join(body_lines).strip()
            logger.debug(f"تم تحليل المقال بنجاح: {file_path}")
            return metadata
            
        except Exception as e:
            logger.error(f"فشل في تحليل المقال: {file_path}", e)
            raise
    
    def get_or_create_tag(self, tag_name: str, site_config: Dict) -> Optional[int]:
        """الحصول على معرف التاج أو إنشاؤه إذا لم يكن موجوداً"""
        tags_url = f"{site_config['site_url']}/wp-json/wp/v2/tags"
        auth = HTTPBasicAuth(site_config['username'], site_config['app_password'])
        
        try:
            # البحث عن التاج الموجود
            response = requests.get(
                tags_url, 
                params={"search": tag_name}, 
                auth=auth,
                timeout=30
            )
            
            if response.status_code == 200 and response.json():
                tag_id = response.json()[0]['id']
                logger.debug(f"تم العثور على التاج الموجود: {tag_name} (ID: {tag_id})")
                return tag_id
            
            # إنشاء تاج جديد
            response = requests.post(
                tags_url, 
                auth=auth, 
                json={"name": tag_name},
                timeout=30
            )
            
            if response.status_code == 201:
                tag_id = response.json()['id']
                logger.success(f"تم إنشاء تاج جديد: {tag_name} (ID: {tag_id})")
                return tag_id
            else:
                logger.warning(f"فشل في إنشاء التاج: {tag_name}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في معالجة التاج: {tag_name}", e)
            return None
    
    def upload_image(self, image_path: str, site_config: Dict) -> Optional[int]:
        """رفع صورة إلى WordPress"""
        if not os.path.exists(image_path):
            logger.warning(f"الصورة غير موجودة: {image_path}")
            return None
        
        media_url = f"{site_config['site_url']}/wp-json/wp/v2/media"
        auth = HTTPBasicAuth(site_config['username'], site_config['app_password'])
        
        try:
            image_name = os.path.basename(image_path)
            mime_type, _ = mimetypes.guess_type(image_path)
            if not mime_type:
                mime_type = "image/webp"
            
            with open(image_path, "rb") as img_file:
                headers = {
                    "Content-Disposition": f"attachment; filename={image_name}",
                    "Content-Type": mime_type
                }
                
                response = requests.post(
                    media_url,
                    headers=headers,
                    auth=auth,
                    data=img_file,
                    timeout=60
                )
                
                if response.status_code == 201:
                    image_id = response.json()['id']
                    logger.success(f"تم رفع الصورة بنجاح: {image_name} (ID: {image_id})")
                    return image_id
                else:
                    logger.error(f"فشل رفع الصورة: {image_name}", extra_data={"status": response.status_code, "response": response.text})
                    return None
                    
        except Exception as e:
            logger.error(f"خطأ في رفع الصورة: {image_path}", e)
            return None
    
    def publish_to_wordpress(self, article: Dict, site_name: str = "mshru3", image_path: str = None, category_id: int = None) -> Tuple[bool, str]:
        """نشر المقال على WordPress"""
        site_config = Config.get_wordpress_site(site_name)
        if not site_config:
            error_msg = f"إعدادات الموقع غير موجودة: {site_name}"
            logger.error(error_msg)
            return False, error_msg
        
        posts_url = f"{site_config['site_url']}/wp-json/wp/v2/posts"
        auth = HTTPBasicAuth(site_config['username'], site_config['app_password'])
        
        try:
            # إعداد بيانات المقال
            post_data = {
                "title": article["seo_title"] or article["title"],
                "content": article["content"],
                "excerpt": article["meta_description"],
                "status": article["status"]
            }
            
            # إضافة التصنيف
            if category_id:
                # استخدام التصنيف المحدد من الواجهة
                post_data["categories"] = [category_id]
                logger.debug(f"تم تعيين التصنيف المحدد (ID: {category_id})")
            elif article["category"] and article["category"] in site_config.get("categories", {}):
                # استخدام التصنيف من المقال إذا لم يتم تحديد تصنيف من الواجهة
                fallback_category_id = site_config["categories"][article["category"]]
                post_data["categories"] = [fallback_category_id]
                logger.debug(f"تم تعيين التصنيف من المقال: {article['category']} (ID: {fallback_category_id})")
            
            # إضافة التاجز
            if article["tags"]:
                tag_ids = []
                for tag in article["tags"]:
                    tag_id = self.get_or_create_tag(tag, site_config)
                    if tag_id:
                        tag_ids.append(tag_id)
                
                if tag_ids:
                    post_data["tags"] = tag_ids
                    logger.debug(f"تم تعيين {len(tag_ids)} تاج للمقال")
            
            # رفع الصورة المميزة
            if image_path:
                image_id = self.upload_image(image_path, site_config)
                if image_id:
                    post_data["featured_media"] = image_id
            
            # نشر المقال
            response = requests.post(
                posts_url,
                auth=auth,
                json=post_data,
                timeout=60
            )
            
            if response.status_code == 201:
                post_url = response.json().get("link", "")
                post_title = article["seo_title"] or article["title"]
                logger.article_published(post_title, site_name, post_url)

                # تسجيل النشر في قاعدة البيانات
                # البحث عن معرف المقال في قاعدة البيانات
                article_stats = db.get_article_stats(article.get("keyword", ""))
                if article_stats:
                    db.add_publication(article_stats["id"], "main", site_name, post_url)

                # تحديث الإحصائيات اليومية
                db.update_daily_stats(articles_published=1)

                return True, post_url
            else:
                error_msg = f"فشل النشر - كود الحالة: {response.status_code}"
                logger.error(error_msg, extra_data={"response": response.text})
                return False, error_msg
                
        except Exception as e:
            error_msg = f"خطأ في نشر المقال: {str(e)}"
            logger.error(error_msg, e)
            return False, error_msg
    
    def move_to_published(self, file_path: str, image_path: str = None) -> None:
        """نقل الملفات إلى مجلد المنشورات"""
        try:
            published_dir = Config.DIRECTORIES["published"]
            filename = os.path.basename(file_path)
            
            # نقل ملف المقال
            destination = os.path.join(published_dir, filename)
            shutil.move(file_path, destination)
            logger.info(f"تم نقل المقال إلى: {destination}")
            
            # نقل الصورة إن وجدت
            if image_path and os.path.exists(image_path):
                image_filename = os.path.basename(image_path)
                image_destination = os.path.join(published_dir, image_filename)
                shutil.move(image_path, image_destination)
                logger.info(f"تم نقل الصورة إلى: {image_destination}")
                
        except Exception as e:
            logger.error(f"فشل في نقل الملفات إلى مجلد المنشورات", e)
    
    def find_article_image(self, article_file: str) -> Optional[str]:
        """البحث عن صورة مرتبطة بالمقال"""
        base_name = os.path.splitext(os.path.basename(article_file))[0]
        article_dir = os.path.dirname(article_file)
        
        # البحث عن صورة بنفس اسم المقال
        for ext in [".jpg", ".jpeg", ".png", ".webp"]:
            image_path = os.path.join(article_dir, base_name + ext)
            if os.path.exists(image_path):
                logger.debug(f"تم العثور على صورة للمقال: {image_path}")
                return image_path
        
        return None
