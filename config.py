"""إعدادات البرنامج الرئيسية - محسن ومنظم"""
import os
import json
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class GeminiModel:
    """فئة لتمثيل نموذج Gemini"""
    name: str
    model_id: str
    description: str
    max_tokens: int
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 40

class Config:
    """إدارة إعدادات البرنامج المحسنة"""
    
    # 🔑 مفاتيح API
    GEMINI_API_KEY = "AIzaSyDiQUr0lCE_D-nbcsxM9YjpwzRUjH-tJdA"
    
    # 🤖 نماذج Gemini المتاحة
    GEMINI_MODELS = {
        "gemini-2.0-flash-exp": GeminiModel(
            name="Gemini 2.0 Flash (تجريبي)",
            model_id="gemini-2.0-flash-exp",
            description="أحدث نموذج تجريبي - سريع ومتطور",
            max_tokens=8192,
            temperature=0.7
        ),
        "gemini-1.5-pro": GeminiModel(
            name="Gemini 1.5 Pro",
            model_id="gemini-1.5-pro",
            description="نموذج متقدم للمهام المعقدة",
            max_tokens=8192,
            temperature=0.7
        ),
        "gemini-1.5-flash": GeminiModel(
            name="Gemini 1.5 Flash",
            model_id="gemini-1.5-flash",
            description="نموذج سريع ومتوازن",
            max_tokens=8192,
            temperature=0.7
        )
    }
    
    # 📁 ملفات التكوين
    SITES_CONFIG_FILE = "sites_config.json"
    USER_SETTINGS_FILE = "user_settings.json"
    
    # 📁 مجلدات النظام
    DIRECTORIES = {
        "articles": "articles",
        "keywords": "keywords", 
        "published": "published",
        "database": "database",
        "prompts": "prompts",
        "logs": "logs",
        "temp": "temp"
    }
    
    # 🤖 إعدادات الذكاء الاصطناعي الافتراضية
    DEFAULT_AI_SETTINGS = {
        "model": "gemini-1.5-flash",  # النموذج الافتراضي
        "max_retries": 3,
        "retry_delay": 5,
        "rate_limit_delay": (6, 15),
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "model_settings": {
            "gemini-2.0-flash-exp": {
                "max_retries": 5,
                "retry_delay": 10,
                "rate_limit_delay": (15, 30)
            },
            "gemini-1.5-pro": {
                "max_retries": 5,
                "retry_delay": 15,
                "rate_limit_delay": (30, 60)
            },
            "gemini-1.5-flash": {
                "max_retries": 4,
                "retry_delay": 8,
                "rate_limit_delay": (10, 20)
            }
        }
    }
    
    # 📝 إعدادات المحتوى
    CONTENT_SETTINGS = {
        "main_article_min_words": 2500,
        "sub_article_min_words": 2000,
        "keyword_density": 1.3,
        "related_keyword_density": (0.4, 0.8),
        "meta_description_max_chars": 160
    }
    
    # 📊 إعدادات قاعدة البيانات
    DATABASE_SETTINGS = {
        "name": "content_manager.db"
    }
    
    # 🌐 إعدادات WordPress الافتراضية
    DEFAULT_WORDPRESS_SITES = {
        "mshru3": {
            "name": "مشروع",
            "username": "m.m3rfa",
            "app_password": "j3mG Ldf9 izsJ kY3J Iri2 snMb",
            "site_url": "https://www.mshru3.com",
            "categories": {
                "تقنية": 94,
                "الربح من الانترنت": 75,
                "سفر وسياحة": 127,
                "صحة": 95,
                "مال و استثمار": 85
            }
        }
    }
    
    # إعدادات المستخدم المحملة
    _user_settings = {}
    _wordpress_sites = {}
    
    @classmethod
    def initialize(cls) -> None:
        """تهيئة النظام وتحميل الإعدادات"""
        cls.ensure_directories()
        cls._load_user_settings()
        cls._load_sites_config()
    
    @classmethod
    def ensure_directories(cls) -> None:
        """إنشاء المجلدات المطلوبة"""
        for dir_name, dir_path in cls.DIRECTORIES.items():
            os.makedirs(dir_path, exist_ok=True)
    
    @classmethod
    def _load_user_settings(cls) -> None:
        """تحميل إعدادات المستخدم"""
        try:
            if os.path.exists(cls.USER_SETTINGS_FILE):
                with open(cls.USER_SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    cls._user_settings = json.load(f)
            else:
                cls._user_settings = cls.DEFAULT_AI_SETTINGS.copy()
                cls._save_user_settings()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات المستخدم: {e}")
            cls._user_settings = cls.DEFAULT_AI_SETTINGS.copy()
    
    @classmethod
    def _save_user_settings(cls) -> None:
        """حفظ إعدادات المستخدم"""
        try:
            with open(cls.USER_SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(cls._user_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات المستخدم: {e}")
    
    @classmethod
    def _load_sites_config(cls) -> None:
        """تحميل إعدادات المواقع"""
        try:
            if os.path.exists(cls.SITES_CONFIG_FILE):
                with open(cls.SITES_CONFIG_FILE, 'r', encoding='utf-8') as f:
                    cls._wordpress_sites = json.load(f)
            else:
                cls._wordpress_sites = cls.DEFAULT_WORDPRESS_SITES.copy()
                cls._save_sites_config()
        except Exception as e:
            print(f"خطأ في تحميل إعدادات المواقع: {e}")
            cls._wordpress_sites = cls.DEFAULT_WORDPRESS_SITES.copy()
    
    @classmethod
    def _save_sites_config(cls) -> None:
        """حفظ إعدادات المواقع"""
        try:
            with open(cls.SITES_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(cls._wordpress_sites, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات المواقع: {e}")
    
    # === إدارة نماذج Gemini ===
    
    @classmethod
    def get_available_models(cls) -> Dict[str, GeminiModel]:
        """الحصول على النماذج المتاحة"""
        return cls.GEMINI_MODELS
    
    @classmethod
    def get_current_model(cls) -> GeminiModel:
        """الحصول على النموذج الحالي"""
        model_id = cls._user_settings.get("model", cls.DEFAULT_AI_SETTINGS["model"])
        return cls.GEMINI_MODELS.get(model_id, cls.GEMINI_MODELS["gemini-1.5-flash"])
    
    @classmethod
    def set_current_model(cls, model_id: str) -> bool:
        """تعيين النموذج الحالي"""
        if model_id in cls.GEMINI_MODELS:
            cls._user_settings["model"] = model_id
            cls._save_user_settings()
            return True
        return False
    
    @classmethod
    def get_model_settings(cls) -> Dict:
        """الحصول على إعدادات النموذج الحالي"""
        current_model = cls.get_current_model()
        model_id = current_model.model_id
        settings = cls._user_settings.copy()
        
        # استخدام إعدادات النموذج المحدد إذا كانت موجودة
        model_specific_settings = settings.get("model_settings", {}).get(model_id, {})
        
        # دمج الإعدادات العامة مع إعدادات النموذج المحدد
        result = {
            "model_id": model_id,
            "max_tokens": current_model.max_tokens,
            "temperature": settings.get("temperature", current_model.temperature),
            "top_p": settings.get("top_p", current_model.top_p),
            "top_k": settings.get("top_k", current_model.top_k),
            "max_retries": model_specific_settings.get("max_retries", settings.get("max_retries", 3)),
            "retry_delay": model_specific_settings.get("retry_delay", settings.get("retry_delay", 5)),
            "rate_limit_delay": model_specific_settings.get("rate_limit_delay", settings.get("rate_limit_delay", (6, 15)))
        }
        
        return result
    
    @classmethod
    def get_model_specific_settings(cls, model_id: str) -> Dict:
        """الحصول على إعدادات نموذج محدد"""
        if model_id not in cls.GEMINI_MODELS:
            return {}
            
        model = cls.GEMINI_MODELS[model_id]
        settings = cls._user_settings.copy()
        model_specific_settings = settings.get("model_settings", {}).get(model_id, {})
        
        result = {
            "model_id": model_id,
            "name": model.name,
            "description": model.description,
            "max_tokens": model.max_tokens,
            "temperature": model_specific_settings.get("temperature", model.temperature),
            "top_p": model_specific_settings.get("top_p", model.top_p),
            "top_k": model_specific_settings.get("top_k", model.top_k),
            "max_retries": model_specific_settings.get("max_retries", settings.get("max_retries", 3)),
            "retry_delay": model_specific_settings.get("retry_delay", settings.get("retry_delay", 5)),
            "rate_limit_delay": model_specific_settings.get("rate_limit_delay", settings.get("rate_limit_delay", (6, 15)))
        }
        
        return result
    
    @classmethod
    def update_model_settings(cls, **kwargs) -> None:
        """تحديث إعدادات النموذج"""
        for key, value in kwargs.items():
            if key in ["temperature", "top_p", "top_k", "max_retries", "retry_delay"]:
                cls._user_settings[key] = value
                
        # تحديث إعدادات نموذج محدد إذا تم تحديد model_id
        if "model_id" in kwargs and "settings" in kwargs:
            model_id = kwargs["model_id"]
            model_settings = kwargs["settings"]
            
            if "model_settings" not in cls._user_settings:
                cls._user_settings["model_settings"] = {}
                
            if model_id not in cls._user_settings["model_settings"]:
                cls._user_settings["model_settings"][model_id] = {}
                
            for key, value in model_settings.items():
                if key in ["temperature", "top_p", "top_k", "max_retries", "retry_delay", "rate_limit_delay"]:
                    cls._user_settings["model_settings"][model_id][key] = value
                    
        cls._save_user_settings()
    
    # === إدارة مواقع WordPress ===
    
    @classmethod
    def get_wordpress_sites(cls) -> Dict:
        """الحصول على جميع مواقع WordPress"""
        return cls._wordpress_sites
    
    @classmethod
    def get_wordpress_site(cls, site_id: str) -> Optional[Dict]:
        """الحصول على موقع WordPress محدد"""
        return cls._wordpress_sites.get(site_id)
    
    @classmethod
    def add_wordpress_site(cls, site_id: str, name: str, username: str, 
                          app_password: str, site_url: str, categories: Dict = None) -> None:
        """إضافة موقع WordPress جديد"""
        cls._wordpress_sites[site_id] = {
            "name": name,
            "username": username,
            "app_password": app_password,
            "site_url": site_url,
            "categories": categories or {}
        }
        cls._save_sites_config()
    
    @classmethod
    def update_wordpress_site(cls, site_id: str, **kwargs) -> bool:
        """تحديث موقع WordPress"""
        if site_id in cls._wordpress_sites:
            cls._wordpress_sites[site_id].update(kwargs)
            cls._save_sites_config()
            return True
        return False
    
    @classmethod
    def remove_wordpress_site(cls, site_id: str) -> bool:
        """حذف موقع WordPress"""
        if site_id in cls._wordpress_sites:
            del cls._wordpress_sites[site_id]
            cls._save_sites_config()
            return True
        return False
    
    @classmethod
    def get_site_ids(cls) -> List[str]:
        """الحصول على معرفات المواقع"""
        return list(cls._wordpress_sites.keys())

# تهيئة النظام عند الاستيراد
Config.initialize()
