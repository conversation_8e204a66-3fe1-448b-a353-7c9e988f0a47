"""
مدير المشاركات - جلب وإدارة المشاركات من WordPress
"""

import requests
import sqlite3
import os
from typing import List, Dict, Optional
from datetime import datetime
import re
from difflib import SequenceMatcher

from config import Config
from logger import logger


class PostsManager:
    """مدير المشاركات من WordPress"""
    
    def __init__(self):
        """تهيئة مدير المشاركات"""
        self.db_path = os.path.join(Config.DIRECTORIES["database"], "posts.db")
        self._init_database()
        logger.info("تم تهيئة مدير المشاركات بنجاح")
    
    def _init_database(self):
        """إنشاء قاعدة بيانات المشاركات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS posts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        site_id TEXT NOT NULL,
                        post_id INTEGER NOT NULL,
                        title TEXT NOT NULL,
                        link TEXT NOT NULL,
                        date TEXT,
                        category TEXT,
                        content TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(site_id, post_id)
                    )
                ''')

                # جدول التصنيفات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS categories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        site_id TEXT NOT NULL,
                        category_id INTEGER NOT NULL,
                        name TEXT NOT NULL,
                        slug TEXT,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(site_id, category_id)
                    )
                ''')

                # جدول تتبع آخر تحديث
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sync_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        site_id TEXT NOT NULL UNIQUE,
                        last_sync_date TEXT,
                        last_post_id INTEGER,
                        total_posts INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()
                logger.info("تم إنشاء قاعدة بيانات المشاركات")
        except Exception as e:
            logger.error("فشل في إنشاء قاعدة بيانات المشاركات", e)
            raise

    def _get_last_sync_info(self, site_id: str) -> Dict:
        """الحصول على معلومات آخر مزامنة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT last_sync_date, last_post_id, total_posts
                    FROM sync_status WHERE site_id = ?
                ''', (site_id,))

                result = cursor.fetchone()
                if result:
                    return {
                        'last_sync_date': result[0],
                        'last_post_id': result[1],
                        'total_posts': result[2] or 0
                    }
                else:
                    return {
                        'last_sync_date': None,
                        'last_post_id': None,
                        'total_posts': 0
                    }
        except Exception as e:
            logger.error(f"فشل في الحصول على معلومات المزامنة لـ {site_id}", e)
            return {'last_sync_date': None, 'last_post_id': None, 'total_posts': 0}

    def _update_sync_info(self, site_id: str, last_post_id: int, total_posts: int):
        """تحديث معلومات المزامنة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                cursor.execute('''
                    INSERT OR REPLACE INTO sync_status
                    (site_id, last_sync_date, last_post_id, total_posts, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (site_id, current_date, last_post_id, total_posts, current_date))

                conn.commit()
                logger.info(f"تم تحديث معلومات المزامنة لـ {site_id}")
        except Exception as e:
            logger.error(f"فشل في تحديث معلومات المزامنة لـ {site_id}", e)

    def _get_last_post_date(self, site_id: str) -> Optional[str]:
        """الحصول على تاريخ آخر مشاركة محفوظة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT date FROM posts
                    WHERE site_id = ? AND date IS NOT NULL
                    ORDER BY date DESC LIMIT 1
                ''', (site_id,))

                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"فشل في الحصول على تاريخ آخر مشاركة لـ {site_id}", e)
            return None
    
    def fetch_all_posts(self, site_id: str, incremental: bool = True) -> List[Dict]:
        """جلب المشاركات من موقع WordPress (تدريجي أو كامل)"""
        try:
            site_config = Config.get_wordpress_site(site_id)
            if not site_config:
                raise ValueError(f"الموقع {site_id} غير موجود")

            base_url = site_config['site_url'].rstrip('/')
            api_url = f"{base_url}/wp-json/wp/v2/posts"

            # الحصول على معلومات آخر مزامنة
            sync_info = self._get_last_sync_info(site_id)

            all_posts = []
            page = 1
            per_page = 50  # تقليل العدد لتجنب انقطاع الاتصال

            if incremental and sync_info['last_sync_date']:
                # استخدام تاريخ آخر مشاركة محفوظة بدلاً من تاريخ المزامنة
                last_post_date = self._get_last_post_date(site_id)
                after_date = last_post_date or sync_info['last_sync_date']

                logger.info(f"بدء الجلب التدريجي من {site_id}")
                logger.info(f"آخر مشاركة محفوظة: {last_post_date}")
                logger.info(f"البحث عن مشاركات بعد: {after_date}")

                # جلب المشاركات الجديدة فقط
                params_base = {
                    'per_page': per_page,
                    'status': 'publish',
                    'after': after_date,
                    'orderby': 'date',
                    'order': 'desc',
                    '_fields': 'id,title,link,date,categories,content'
                }
                max_pages = 50  # حد أعلى للجلب التدريجي
            else:
                logger.info(f"بدء الجلب الكامل من {site_id}")
                params_base = {
                    'per_page': per_page,
                    'status': 'publish',
                    'orderby': 'date',
                    'order': 'desc',
                    '_fields': 'id,title,link,date,categories,content'
                }
                max_pages = 10  # حد أقل للجلب الكامل لتجنب انقطاع الاتصال

            pages_fetched = 0
            latest_post_id = sync_info['last_post_id']
            found_old_post = False  # علامة للتوقف عند العثور على مشاركة قديمة

            while pages_fetched < max_pages and not found_old_post:
                params = {**params_base, 'page': page}

                try:
                    response = requests.get(api_url, params=params, timeout=15)

                    if response.status_code == 400 and page > 1:
                        # انتهت الصفحات
                        break

                    response.raise_for_status()
                    posts = response.json()

                    if not posts:
                        break

                    # معالجة المشاركات
                    new_posts_count = 0
                    for post in posts:
                        post_id = post.get('id', 0)

                        # في الجلب التدريجي، توقف عند العثور على مشاركة قديمة
                        if incremental and sync_info['last_post_id'] and post_id <= sync_info['last_post_id']:
                            logger.info(f"تم العثور على مشاركة قديمة (ID: {post_id}), التوقف هنا")
                            found_old_post = True
                            break

                        processed_post = self._process_post(post, site_id)
                        if processed_post:
                            all_posts.append(processed_post)
                            new_posts_count += 1

                            # تحديث آخر معرف مشاركة
                            if not latest_post_id or post_id > latest_post_id:
                                latest_post_id = post_id

                    logger.info(f"تم جلب {new_posts_count} مشاركة جديدة من الصفحة {page}")

                    # إذا لم نجد مشاركات جديدة في الجلب التدريجي، توقف
                    if incremental and new_posts_count == 0:
                        logger.info("لا توجد مشاركات جديدة في هذه الصفحة")
                        break

                    page += 1
                    pages_fetched += 1

                except requests.exceptions.RequestException as e:
                    logger.warning(f"خطأ في الصفحة {page}: {str(e)}")
                    break

            if all_posts:
                # حفظ المشاركات الجديدة في قاعدة البيانات
                if incremental:
                    self._save_new_posts_to_db(all_posts, site_id)
                    # تحديث معلومات المزامنة
                    total_posts = sync_info['total_posts'] + len(all_posts)
                    self._update_sync_info(site_id, latest_post_id, total_posts)
                    logger.success(f"تم جلب {len(all_posts)} مشاركة جديدة من {site_id}")
                else:
                    self._save_posts_to_db(all_posts, site_id)
                    # تحديث معلومات المزامنة للجلب الكامل
                    self._update_sync_info(site_id, latest_post_id, len(all_posts))
                    logger.success(f"تم جلب {len(all_posts)} مشاركة في الجلب الكامل من {site_id}")
            else:
                if incremental:
                    logger.info(f"لا توجد مشاركات جديدة في {site_id}")
                else:
                    logger.warning(f"لم يتم جلب أي مشاركات من {site_id}")

            return all_posts

        except Exception as e:
            logger.error(f"فشل في جلب المشاركات من {site_id}", e)
            raise
    
    def _process_post(self, post: Dict, site_id: str) -> Optional[Dict]:
        """معالجة مشاركة واحدة"""
        try:
            # استخراج العنوان
            title = post.get('title', {}).get('rendered', '').strip()
            if not title:
                return None
            
            # تنظيف العنوان من HTML
            title = re.sub(r'<[^>]+>', '', title)
            
            # استخراج المحتوى
            content = post.get('content', {}).get('rendered', '')
            content = re.sub(r'<[^>]+>', ' ', content)  # إزالة HTML
            content = ' '.join(content.split())  # تنظيف المسافات
            
            return {
                'site_id': site_id,
                'post_id': post.get('id'),
                'title': title,
                'link': post.get('link', ''),
                'date': post.get('date', '').split('T')[0] if post.get('date') else '',
                'category': self._get_category_name(post.get('categories', []), site_id),
                'content': content[:1000]  # أول 1000 حرف للبحث
            }
            
        except Exception as e:
            logger.error(f"فشل في معالجة المشاركة: {post.get('id', 'unknown')}", e)
            return None
    
    def _get_category_name(self, category_ids: List[int], site_id: str) -> str:
        """الحصول على اسم الفئة"""
        if not category_ids:
            return "غير محدد"
        
        try:
            site_config = Config.get_wordpress_site(site_id)
            categories = site_config.get('categories', {})
            
            # البحث عن الفئة بالمعرف
            for cat_name, cat_id in categories.items():
                if cat_id in category_ids:
                    return cat_name
            
            return f"فئة {category_ids[0]}"
            
        except Exception:
            return "غير محدد"
    
    def _save_posts_to_db(self, posts: List[Dict], site_id: str):
        """حفظ المشاركات في قاعدة البيانات (مسح الكل أولاً)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # مسح المشاركات القديمة لهذا الموقع
                cursor.execute('DELETE FROM posts WHERE site_id = ?', (site_id,))

                # إدراج المشاركات الجديدة
                for post in posts:
                    cursor.execute('''
                        INSERT OR REPLACE INTO posts
                        (site_id, post_id, title, link, date, category, content)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        post['site_id'],
                        post['post_id'],
                        post['title'],
                        post['link'],
                        post['date'],
                        post['category'],
                        post['content']
                    ))

                conn.commit()
                logger.info(f"تم حفظ {len(posts)} مشاركة في قاعدة البيانات")

        except Exception as e:
            logger.error("فشل في حفظ المشاركات في قاعدة البيانات", e)
            raise

    def _save_new_posts_to_db(self, posts: List[Dict], site_id: str):
        """حفظ المشاركات الجديدة فقط في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # إدراج المشاركات الجديدة فقط
                for post in posts:
                    cursor.execute('''
                        INSERT OR REPLACE INTO posts
                        (site_id, post_id, title, link, date, category, content)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        post['site_id'],
                        post['post_id'],
                        post['title'],
                        post['link'],
                        post['date'],
                        post['category'],
                        post['content']
                    ))

                conn.commit()
                logger.info(f"تم حفظ {len(posts)} مشاركة جديدة في قاعدة البيانات")

        except Exception as e:
            logger.error("فشل في حفظ المشاركات الجديدة في قاعدة البيانات", e)
            raise
    
    def get_stored_posts(self, site_id: str = None) -> List[Dict]:
        """الحصول على المشاركات المحفوظة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if site_id:
                    cursor.execute('''
                        SELECT site_id, post_id, title, link, date, category, content
                        FROM posts WHERE site_id = ?
                        ORDER BY date DESC
                    ''', (site_id,))
                else:
                    cursor.execute('''
                        SELECT site_id, post_id, title, link, date, category, content
                        FROM posts ORDER BY date DESC
                    ''')
                
                posts = []
                for row in cursor.fetchall():
                    posts.append({
                        'site_id': row[0],
                        'post_id': row[1],
                        'title': row[2],
                        'link': row[3],
                        'date': row[4],
                        'category': row[5],
                        'content': row[6]
                    })
                
                return posts
                
        except Exception as e:
            logger.error("فشل في جلب المشاركات المحفوظة", e)
            return []
    
    def find_related_posts(self, keyword: str, site_id: str, category_id: int = None, limit: int = 10) -> List[Dict]:
        """البحث عن المشاركات المرتبطة بالكلمة المفتاحية والتصنيف"""
        try:
            posts = self.get_stored_posts(site_id)
            if not posts:
                return []

            # الحصول على اسم التصنيف إذا تم تمرير معرف
            target_category_name = None
            if category_id:
                target_category_name = self._get_category_name_by_id(category_id, site_id)

            # حساب التشابه مع كل مشاركة
            scored_posts = []
            keyword_lower = keyword.lower()

            for post in posts:
                title_lower = post['title'].lower()
                content_lower = post['content'].lower()

                # حساب نقاط التشابه الأساسية
                title_score = self._calculate_similarity(keyword_lower, title_lower)
                content_score = self._calculate_similarity(keyword_lower, content_lower) * 0.5

                # البحث عن الكلمات المشتركة
                keyword_words = set(keyword_lower.split())
                title_words = set(title_lower.split())
                content_words = set(content_lower.split())

                common_title = len(keyword_words.intersection(title_words))
                common_content = len(keyword_words.intersection(content_words))

                # النقاط الأساسية
                base_score = title_score + content_score + (common_title * 0.3) + (common_content * 0.1)

                # إضافة نقاط إضافية للمشاركات من نفس التصنيف
                category_bonus = 0
                if target_category_name and post.get('category'):
                    if post['category'].lower() == target_category_name.lower():
                        category_bonus = 0.5  # نقاط إضافية كبيرة لنفس التصنيف
                        logger.debug(f"مكافأة التصنيف: {post['title']} - {post['category']}")
                    elif target_category_name.lower() in post['category'].lower() or post['category'].lower() in target_category_name.lower():
                        category_bonus = 0.2  # نقاط أقل للتصنيفات المشابهة

                total_score = base_score + category_bonus

                if total_score > 0.1:  # حد أدنى للتشابه
                    scored_posts.append({
                        **post,
                        'similarity_score': total_score,
                        'category_match': category_bonus > 0,
                        'category_bonus': category_bonus
                    })

            # ترتيب حسب نقاط التشابه (التصنيف المطابق أولاً)
            scored_posts.sort(key=lambda x: (x['category_bonus'], x['similarity_score']), reverse=True)

            # تسجيل النتائج للمراجعة
            if scored_posts:
                logger.info(f"تم العثور على {len(scored_posts)} مشاركة مرتبطة بـ {keyword}")
                if target_category_name:
                    category_matches = [p for p in scored_posts if p['category_match']]
                    logger.info(f"منها {len(category_matches)} من نفس التصنيف: {target_category_name}")

            return scored_posts[:limit]

        except Exception as e:
            logger.error(f"فشل في البحث عن المشاركات المرتبطة بـ {keyword}", e)
            return []
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """حساب التشابه بين نصين"""
        return SequenceMatcher(None, text1, text2).ratio()

    def _get_category_name_by_id(self, category_id: int, site_id: str) -> Optional[str]:
        """الحصول على اسم التصنيف بالمعرف"""
        try:
            categories = self.get_stored_categories(site_id)
            for category in categories:
                if category['category_id'] == category_id:
                    return category['name']
            return None
        except Exception as e:
            logger.error(f"فشل في الحصول على اسم التصنيف {category_id}", e)
            return None
    
    def clear_all_posts(self):
        """مسح جميع المشاركات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM posts')
                conn.commit()
                logger.info("تم مسح جميع المشاركات")
        except Exception as e:
            logger.error("فشل في مسح المشاركات", e)
            raise

    def reset_sync_status(self, site_id: str):
        """إعادة تعيين حالة المزامنة لجلب كامل"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM sync_status WHERE site_id = ?', (site_id,))
                conn.commit()
                logger.info(f"تم إعادة تعيين حالة المزامنة لـ {site_id}")
        except Exception as e:
            logger.error(f"فشل في إعادة تعيين حالة المزامنة لـ {site_id}", e)

    def get_sync_stats(self, site_id: str) -> Dict:
        """الحصول على إحصائيات المزامنة"""
        try:
            sync_info = self._get_last_sync_info(site_id)
            stored_posts = len(self.get_stored_posts(site_id))

            return {
                'last_sync': sync_info['last_sync_date'],
                'total_stored': stored_posts,
                'last_post_id': sync_info['last_post_id'],
                'is_synced': sync_info['last_sync_date'] is not None
            }
        except Exception as e:
            logger.error(f"فشل في الحصول على إحصائيات المزامنة لـ {site_id}", e)
            return {
                'last_sync': None,
                'total_stored': 0,
                'last_post_id': None,
                'is_synced': False
            }

    def fetch_categories(self, site_id: str) -> List[Dict]:
        """جلب التصنيفات من موقع WordPress"""
        try:
            site_config = Config.get_wordpress_site(site_id)
            if not site_config:
                raise ValueError(f"الموقع {site_id} غير موجود")

            base_url = site_config['site_url'].rstrip('/')
            api_url = f"{base_url}/wp-json/wp/v2/categories"

            all_categories = []
            page = 1
            per_page = 100

            logger.info(f"بدء جلب التصنيفات من {site_id}")

            while True:
                params = {
                    'page': page,
                    'per_page': per_page,
                    '_fields': 'id,name,slug,description,count'
                }

                response = requests.get(api_url, params=params, timeout=30)

                if response.status_code == 400 and page > 1:
                    # انتهت الصفحات
                    break

                response.raise_for_status()
                categories = response.json()

                if not categories:
                    break

                # معالجة التصنيفات
                for category in categories:
                    processed_category = self._process_category(category, site_id)
                    if processed_category:
                        all_categories.append(processed_category)

                logger.info(f"تم جلب {len(categories)} تصنيف من الصفحة {page}")
                page += 1

            # حفظ التصنيفات في قاعدة البيانات
            self._save_categories_to_db(all_categories, site_id)

            logger.success(f"تم جلب {len(all_categories)} تصنيف من {site_id}")
            return all_categories

        except Exception as e:
            logger.error(f"فشل في جلب التصنيفات من {site_id}", e)
            raise

    def _process_category(self, category: Dict, site_id: str) -> Optional[Dict]:
        """معالجة تصنيف واحد"""
        try:
            name = category.get('name', '').strip()
            if not name:
                return None

            return {
                'site_id': site_id,
                'category_id': category.get('id'),
                'name': name,
                'slug': category.get('slug', ''),
                'description': category.get('description', ''),
                'count': category.get('count', 0)
            }

        except Exception as e:
            logger.error(f"فشل في معالجة التصنيف: {category.get('id', 'unknown')}", e)
            return None

    def _save_categories_to_db(self, categories: List[Dict], site_id: str):
        """حفظ التصنيفات في قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # مسح التصنيفات القديمة لهذا الموقع
                cursor.execute('DELETE FROM categories WHERE site_id = ?', (site_id,))

                # إدراج التصنيفات الجديدة
                for category in categories:
                    cursor.execute('''
                        INSERT OR REPLACE INTO categories
                        (site_id, category_id, name, slug, description)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        category['site_id'],
                        category['category_id'],
                        category['name'],
                        category['slug'],
                        category['description']
                    ))

                conn.commit()
                logger.info(f"تم حفظ {len(categories)} تصنيف في قاعدة البيانات")

        except Exception as e:
            logger.error("فشل في حفظ التصنيفات في قاعدة البيانات", e)
            raise

    def get_stored_categories(self, site_id: str = None) -> List[Dict]:
        """الحصول على التصنيفات المحفوظة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if site_id:
                    cursor.execute('''
                        SELECT site_id, category_id, name, slug, description
                        FROM categories WHERE site_id = ?
                        ORDER BY name
                    ''', (site_id,))
                else:
                    cursor.execute('''
                        SELECT site_id, category_id, name, slug, description
                        FROM categories ORDER BY name
                    ''')

                categories = []
                for row in cursor.fetchall():
                    categories.append({
                        'site_id': row[0],
                        'category_id': row[1],
                        'name': row[2],
                        'slug': row[3],
                        'description': row[4]
                    })

                return categories

        except Exception as e:
            logger.error("فشل في جلب التصنيفات المحفوظة", e)
            return []
