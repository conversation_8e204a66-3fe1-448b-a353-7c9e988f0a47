"""
نظام تسجيل الأحداث المتقدم
"""
import logging
import os
from datetime import datetime
from typing import Optional

class Logger:
    """مدير نظام التسجيل"""
    
    def __init__(self, name: str = "ContentManager", log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """إعداد نظام التسجيل"""
        # إنشاء مجلد السجلات
        os.makedirs(self.log_dir, exist_ok=True)
        
        # إنشاء اسم ملف السجل بالتاريخ
        log_filename = f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        log_path = os.path.join(self.log_dir, log_filename)
        
        # إعداد التنسيق
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # إعداد المسجل الرئيسي
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # تجنب إضافة handlers متعددة
        if not self.logger.handlers:
            # معالج الملف
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            
            # معالج وحدة التحكم
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            
            # إضافة المعالجات
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def info(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل معلومات عامة"""
        if extra_data:
            message = f"{message} | البيانات الإضافية: {extra_data}"
        self.logger.info(message)
    
    def success(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل عملية ناجحة"""
        message = f"✅ {message}"
        if extra_data:
            message = f"{message} | البيانات: {extra_data}"
        self.logger.info(message)
    
    def warning(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل تحذير"""
        message = f"⚠️ {message}"
        if extra_data:
            message = f"{message} | البيانات: {extra_data}"
        self.logger.warning(message)
    
    def error(self, message: str, exception: Optional[Exception] = None, extra_data: Optional[dict] = None) -> None:
        """تسجيل خطأ"""
        message = f"❌ {message}"
        if extra_data:
            message = f"{message} | البيانات: {extra_data}"
        if exception:
            message = f"{message} | الاستثناء: {str(exception)}"
        self.logger.error(message, exc_info=exception is not None)
    
    def debug(self, message: str, extra_data: Optional[dict] = None) -> None:
        """تسجيل معلومات التطوير"""
        if extra_data:
            message = f"{message} | البيانات: {extra_data}"
        self.logger.debug(message)
    
    def article_generated(self, keyword: str, file_path: str, word_count: int) -> None:
        """تسجيل إنتاج مقال جديد"""
        self.success(f"تم إنتاج مقال جديد", {
            "الكلمة المفتاحية": keyword,
            "مسار الملف": file_path,
            "عدد الكلمات": word_count
        })
    
    def article_published(self, title: str, site: str, url: str) -> None:
        """تسجيل نشر مقال"""
        self.success(f"تم نشر مقال", {
            "العنوان": title,
            "الموقع": site,
            "الرابط": url
        })
    
    def api_call(self, service: str, endpoint: str, status: str, response_time: float = None) -> None:
        """تسجيل استدعاء API"""
        data = {
            "الخدمة": service,
            "النقطة": endpoint,
            "الحالة": status
        }
        if response_time:
            data["وقت الاستجابة"] = f"{response_time:.2f}s"
        
        if status == "success":
            self.info(f"استدعاء API ناجح", data)
        else:
            self.error(f"فشل استدعاء API", extra_data=data)

# إنشاء مثيل عام للاستخدام
logger = Logger()
