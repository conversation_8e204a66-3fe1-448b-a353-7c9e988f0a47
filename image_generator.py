"""
مولد الصور للمقالات باستخدام خدمات مجانية
"""

import os
import requests
import json
import time
from typing import Optional, List, Dict
from PIL import Image, ImageDraw, ImageFont
import io
import base64
from config import Config
from logger import logger

class ImageGenerator:
    """مولد الصور للمقالات"""
    
    def __init__(self):
        """تهيئة مولد الصور"""
        self.images_dir = os.path.join(Config.DIRECTORIES.get("temp", "temp"), "images")
        os.makedirs(self.images_dir, exist_ok=True)
        
        # خدمات الصور المجانية
        self.free_services = {
            "unsplash": {
                "url": "https://api.unsplash.com/search/photos",
                "key": "demo",  # مفتاح تجريبي
                "per_page": 10
            },
            "pixabay": {
                "url": "https://pixabay.com/api/",
                "key": "demo",  # يحتاج مفتاح مجاني
                "per_page": 20
            }
        }
    
    def generate_article_image(self, keyword: str, article_title: str) -> Optional[str]:
        """إنتاج صورة للمقال"""
        try:
            # محاولة البحث عن صورة مناسبة
            image_path = self._search_and_download_image(keyword, article_title)
            
            if not image_path:
                # إنشاء صورة نصية كبديل
                image_path = self._create_text_image(article_title, keyword)
            
            return image_path
            
        except Exception as e:
            logger.error(f"فشل في إنتاج صورة للمقال: {keyword}", e)
            return None
    
    def _search_and_download_image(self, keyword: str, title: str) -> Optional[str]:
        """البحث عن صورة وتحميلها"""
        try:
            # ترجمة الكلمة المفتاحية للإنجليزية (بسيط)
            english_keyword = self._translate_to_english(keyword)
            
            # البحث في Unsplash
            image_url = self._search_unsplash(english_keyword)
            
            if image_url:
                return self._download_image(image_url, keyword)
            
            # البحث في مصادر أخرى إذا فشل Unsplash
            return None
            
        except Exception as e:
            logger.error(f"فشل في البحث عن صورة: {keyword}", e)
            return None
    
    def _search_unsplash(self, keyword: str) -> Optional[str]:
        """البحث في Unsplash"""
        try:
            # استخدام API مجاني محدود
            url = f"https://source.unsplash.com/800x600/?{keyword}"
            
            # التحقق من توفر الصورة
            response = requests.head(url, timeout=10)
            if response.status_code == 200:
                return url
            
            return None
            
        except Exception as e:
            logger.error(f"فشل في البحث في Unsplash: {keyword}", e)
            return None
    
    def _download_image(self, url: str, keyword: str) -> Optional[str]:
        """تحميل الصورة"""
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                # إنشاء اسم ملف آمن
                safe_filename = keyword.replace(" ", "_").replace("؟", "") + ".jpg"
                image_path = os.path.join(self.images_dir, safe_filename)
                
                # حفظ الصورة
                with open(image_path, 'wb') as f:
                    f.write(response.content)
                
                logger.success(f"تم تحميل الصورة: {image_path}")
                return image_path
            
            return None
            
        except Exception as e:
            logger.error(f"فشل في تحميل الصورة: {url}", e)
            return None
    
    def _create_text_image(self, title: str, keyword: str) -> str:
        """إنشاء صورة نصية كبديل"""
        try:
            # إعدادات الصورة
            width, height = 800, 600
            background_color = (67, 126, 234)  # لون أزرق جميل
            text_color = (255, 255, 255)
            
            # إنشاء الصورة
            image = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(image)
            
            # محاولة استخدام خط عربي
            try:
                font_title = ImageFont.truetype("arial.ttf", 40)
                font_subtitle = ImageFont.truetype("arial.ttf", 24)
            except:
                font_title = ImageFont.load_default()
                font_subtitle = ImageFont.load_default()
            
            # تقسيم النص إلى أسطر
            title_lines = self._wrap_text(title, font_title, width - 100)
            
            # حساب الموضع
            total_height = len(title_lines) * 50 + 80
            start_y = (height - total_height) // 2
            
            # رسم العنوان
            current_y = start_y
            for line in title_lines:
                bbox = draw.textbbox((0, 0), line, font=font_title)
                text_width = bbox[2] - bbox[0]
                x = (width - text_width) // 2
                draw.text((x, current_y), line, font=font_title, fill=text_color)
                current_y += 50
            
            # رسم الكلمة المفتاحية
            bbox = draw.textbbox((0, 0), keyword, font=font_subtitle)
            text_width = bbox[2] - bbox[0]
            x = (width - text_width) // 2
            draw.text((x, current_y + 30), keyword, font=font_subtitle, fill=(200, 200, 200))
            
            # حفظ الصورة
            safe_filename = keyword.replace(" ", "_").replace("؟", "") + "_text.png"
            image_path = os.path.join(self.images_dir, safe_filename)
            image.save(image_path, 'PNG')
            
            logger.success(f"تم إنشاء صورة نصية: {image_path}")
            return image_path
            
        except Exception as e:
            logger.error(f"فشل في إنشاء صورة نصية: {keyword}", e)
            return None
    
    def _wrap_text(self, text: str, font, max_width: int) -> List[str]:
        """تقسيم النص إلى أسطر"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = ImageDraw.Draw(Image.new('RGB', (1, 1))).textbbox((0, 0), test_line, font=font)
            if bbox[2] - bbox[0] <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def _translate_to_english(self, arabic_text: str) -> str:
        """ترجمة بسيطة للكلمات الشائعة"""
        translations = {
            "تقنية": "technology",
            "صحة": "health",
            "طعام": "food",
            "سفر": "travel",
            "تعليم": "education",
            "رياضة": "sports",
            "أعمال": "business",
            "فن": "art",
            "موسيقى": "music",
            "طبيعة": "nature",
            "مدينة": "city",
            "بحر": "sea",
            "جبل": "mountain",
            "سماء": "sky"
        }
        
        # البحث عن ترجمة
        for arabic, english in translations.items():
            if arabic in arabic_text:
                return english
        
        # إرجاع نص عام إذا لم توجد ترجمة
        return "abstract,modern,business"
    
    def get_image_info(self, image_path: str) -> Dict:
        """الحصول على معلومات الصورة"""
        try:
            if not os.path.exists(image_path):
                return {}
            
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "size": os.path.getsize(image_path)
                }
        except Exception as e:
            logger.error(f"فشل في الحصول على معلومات الصورة: {image_path}", e)
            return {}

# إنشاء مثيل عام للاستخدام
image_generator = ImageGenerator()
